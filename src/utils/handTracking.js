// Streamlined Hand Tracking Utilities
import { gestureRecognizer } from './gestureRecognition.js';

// Hand landmark joint connections
const fingerJoints = {
  thumb: [0,1,2,3,4],
  indexFinger: [0,5,6,7,8],
  middleFinger: [0,9,10,11,12],
  ringFinger: [0,13,14,15,16],
  pinky: [0,17,18,19,20]
};

// Utility functions
const distance = (p1, p2) => Math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2);
const calculateHandSize = (landmarks) => {
  const wrist = landmarks[0];
  const middleTip = landmarks[12];
  const thumbTip = landmarks[4];
  const pinkyTip = landmarks[20];
  const primary = distance(wrist, middleTip);
  const span = distance(thumbTip, pinkyTip);
  return primary * 0.7 + span * 0.3;
};

/**
 * Streamlined Hand Path Tracker
 */
class HandPathTracker {
  constructor(maxPoints = 50, fadeTime = 2000) {
    this.maxPoints = maxPoints;
    this.fadeTime = fadeTime;
    this.pathPoints = [];
    this.isEnabled = true;
  }

  addPoint(position) {
    if (!this.isEnabled || !position) return;
    this.pathPoints.push({
      x: position.x,
      y: position.y,
      timestamp: Date.now(),
      alpha: 1.0
    });
    if (this.pathPoints.length > this.maxPoints) this.pathPoints.shift();
  }

  updateAndDraw(ctx) {
    if (!this.isEnabled || !ctx || this.pathPoints.length < 2) return;
    
    const currentTime = Date.now();
    this.pathPoints = this.pathPoints.filter(point => {
      const age = currentTime - point.timestamp;
      point.alpha = Math.max(0, 1 - (age / this.fadeTime));
      return point.alpha > 0;
    });
    
    ctx.save();
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Draw trail
    for (let i = 1; i < this.pathPoints.length; i++) {
      const prev = this.pathPoints[i - 1];
      const curr = this.pathPoints[i];
      ctx.strokeStyle = `rgba(0, 255, 255, ${curr.alpha * 0.8})`;
      ctx.lineWidth = Math.max(1, curr.alpha * 4);
      ctx.beginPath();
      ctx.moveTo(prev.x, prev.y);
      ctx.lineTo(curr.x, curr.y);
      ctx.stroke();
    }
    
    // Draw dots
    this.pathPoints.forEach((point, i) => {
      if (i % 3 === 0) {
        ctx.fillStyle = `rgba(0, 255, 255, ${point.alpha * 0.6})`;
        ctx.beginPath();
        ctx.arc(point.x, point.y, point.alpha * 3, 0, 2 * Math.PI);
        ctx.fill();
      }
    });
    
    ctx.restore();
  }

  clear() { this.pathPoints = []; }
  toggle() { 
    this.isEnabled = !this.isEnabled;
    if (!this.isEnabled) this.clear();
  }
}

/**
 * Enhanced Hand Detection with AI improvements
 */
class EnhancedHandDetector {
  constructor() {
    this.confidenceHistory = [];
    this.positionHistory = [];
    this.smoothingFactor = 0.3;
    this.confidenceThreshold = 0.7;
    this.maxHistory = 10;
  }

  processDetection(predictions) {
    if (!predictions || predictions.length === 0) {
      return {
        isValid: false,
        confidence: 0,
        smoothedPosition: null,
        status: 'No hand detected'
      };
    }

    const detection = predictions[0];
    const rawConfidence = detection.handInViewConfidence || 1.0;
    
    // Update confidence history
    this.confidenceHistory.push(rawConfidence);
    if (this.confidenceHistory.length > this.maxHistory) {
      this.confidenceHistory.shift();
    }
    
    // Calculate smoothed confidence
    const smoothedConfidence = this.confidenceHistory.reduce((sum, c) => sum + c, 0) / this.confidenceHistory.length;
    
    // Update position history for smoothing
    const rawPosition = {
      x: detection.landmarks[5][0], // Index finger base
      y: detection.landmarks[5][1]
    };
    
    this.positionHistory.push(rawPosition);
    if (this.positionHistory.length > this.maxHistory) {
      this.positionHistory.shift();
    }
    
    // Calculate smoothed position
    const smoothedPosition = this.calculateSmoothedPosition();
    
    // Determine status
    let status = 'Hand detected';
    if (smoothedConfidence < this.confidenceThreshold) {
      status = 'Low confidence detection';
    } else if (smoothedConfidence > 0.9) {
      status = 'High quality tracking';
    }
    
    return {
      isValid: smoothedConfidence >= this.confidenceThreshold,
      confidence: smoothedConfidence,
      smoothedPosition,
      status,
      landmarks: detection.landmarks
    };
  }

  calculateSmoothedPosition() {
    if (this.positionHistory.length === 0) return null;
    
    // Weighted average with more weight on recent positions
    let totalWeight = 0;
    let weightedX = 0;
    let weightedY = 0;
    
    this.positionHistory.forEach((pos, index) => {
      const weight = (index + 1) / this.positionHistory.length;
      weightedX += pos.x * weight;
      weightedY += pos.y * weight;
      totalWeight += weight;
    });
    
    return {
      x: weightedX / totalWeight,
      y: weightedY / totalWeight
    };
  }
}

/**
 * Streamlined hand drawing function
 */
export const drawHand = (predictions, ctx, enable3D = false, circularMotionController = null) => {
  // Process detection with AI enhancements
  const detectionResult = enhancedDetector.processDetection(predictions);
  
  let handState = {
    isPinched: false,
    position: { x: 0, y: 0 },
    fingerSpread: 0,
    isTracking: detectionResult.isValid,
    handSize: 0,
    depth: 0,
    landmarks: null,
    confidence: detectionResult.confidence,
    circularMotion: null,
    detectionStatus: detectionResult.status,
    smoothedPosition: detectionResult.smoothedPosition,
    recognizedGesture: null,
    gestureConfidence: 0,
    gestureAction: null
  };

  if (detectionResult.isValid && detectionResult.landmarks) {
    const landmarks = detectionResult.landmarks;
    handState.landmarks = landmarks;
    
    // Perform gesture recognition
    const gestureResult = gestureRecognizer.recognizeGesture(landmarks);
    handState.recognizedGesture = gestureResult.gesture;
    handState.gestureConfidence = gestureResult.confidence;
    handState.gestureAction = gestureResult.action;

    // Draw hand points
    landmarks.forEach(([x, y], i) => {
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, 2 * Math.PI);
      if (enable3D) {
        const handSize = calculateHandSize(landmarks);
        const intensity = Math.max(0.3, Math.min(1.0, handSize / 150));
        ctx.fillStyle = `rgb(0, ${Math.floor(255 * intensity)}, 0)`;
      } else {
        ctx.fillStyle = '#00FF00';
      }
      ctx.fill();
    });

    // Draw finger connections
    Object.values(fingerJoints).forEach(points => {
      for (let i = 0; i < points.length - 1; i++) {
        const [x1, y1] = landmarks[points[i]];
        const [x2, y2] = landmarks[points[i + 1]];
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.strokeStyle = enable3D ? '#00FF88' : '#00FF00';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });

    // Use smoothed position if available
    handState.position = detectionResult.smoothedPosition || {
      x: landmarks[5][0],
      y: landmarks[5][1]
    };
    
    // Add point to path tracker
    handPathTracker.addPoint(handState.position);

    // Update circular motion tracking
    if (circularMotionController) {
      const indexTip = landmarks[8];
      circularMotionController.updatePosition({ x: indexTip[0], y: indexTip[1] });
      handState.circularMotion = circularMotionController.getState();
    }

    // Calculate 3D properties
    if (enable3D) {
      handState.handSize = calculateHandSize(landmarks);
      const baseSize = 120;
      const depthSensitivity = 0.3;
      handState.depth = (handState.handSize - baseSize) * depthSensitivity;
    }

    // Check for pinch
    const thumbTip = landmarks[4];
    const indexTip = landmarks[8];
    const pinchDistance = distance(thumbTip, indexTip);
    handState.isPinched = pinchDistance < 30;

    // Calculate finger spread when pinched
    if (handState.isPinched) {
      handState.fingerSpread = distance(landmarks[2], landmarks[17]);
    }

    // Draw circular motion feedback
    if (circularMotionController) {
      circularMotionController.drawFeedback(ctx);
    }
  }

  // Draw hand path trail
  handPathTracker.updateAndDraw(ctx);
  
  // Draw enhanced UI information
  drawEnhancedUI(ctx, handState, enable3D);

  return handState;
};

/**
 * Draw enhanced UI information
 */
function drawEnhancedUI(ctx, handState, enable3D) {
  if (!ctx) return;
  
  ctx.save();
  
  // Draw detection status
  const statusY = enable3D ? 200 : 120;
  ctx.fillStyle = handState.isTracking ? '#00FF00' : '#FF6B6B';
  ctx.font = 'bold 14px Arial';
  ctx.fillText(`Status: ${handState.detectionStatus || 'Unknown'}`, 10, statusY);
  
  // Draw confidence meter
  const confidenceY = statusY + 20;
  ctx.fillStyle = '#FFFFFF';
  ctx.font = '12px Arial';
  ctx.fillText(`Confidence: ${(handState.confidence * 100).toFixed(1)}%`, 10, confidenceY);
  
  // Draw gesture recognition info
  if (handState.recognizedGesture) {
    const gestureY = confidenceY + 25;
    ctx.fillStyle = '#FF6B6B';
    ctx.font = 'bold 12px Arial';
    ctx.fillText(`Gesture: ${handState.recognizedGesture}`, 10, gestureY);
  }
  
  ctx.restore();
}

// Global instances
const handPathTracker = new HandPathTracker();
const enhancedDetector = new EnhancedHandDetector();

// Export path tracker for external control
export { handPathTracker };
