/**
 * Example and Test File for Enhanced 3D Movement System
 * 
 * This file demonstrates how to use the new 3D movement system
 * and provides examples for customization and testing.
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { createMovement3D, MovementConfig } from './freeMovement.js';

/**
 * Example 1: Basic 3D Movement Setup
 * Shows how to create and use the 3D movement system with default settings
 */
export function basicMovement3DExample() {
  // Create a 3D movement controller with default configuration
  const movement3D = createMovement3D();
  
  console.log('Basic 3D Movement System Created');
  console.log('Default Configuration:', movement3D.getConfig());
  
  return movement3D;
}

/**
 * Example 2: Custom Configuration
 * Demonstrates how to customize the 3D movement system for different use cases
 */
export function customMovement3DExample() {
  // Custom configuration for more sensitive movement
  const customConfig = {
    sensitivity: {
      x: 1.2,    // More sensitive left/right
      y: 1.0,    // Normal up/down
      z: 0.8     // Moderate depth sensitivity
    },
    interpolation: {
      x: 0.2,    // More responsive X movement
      y: 0.15,   // Slightly responsive Y movement
      z: 0.05    // Very smooth depth movement
    },
    boundaries: {
      x: { min: -60, max: 60 },    // Wider left/right bounds
      y: { min: -50, max: 50 },    // Taller up/down bounds
      z: { min: -40, max: 40 }     // Deeper forward/backward bounds
    },
    depth: {
      baseHandSize: 130,           // Different reference hand size
      minHandSize: 90,             // Minimum detectable hand size
      maxHandSize: 220,            // Maximum detectable hand size
      depthRange: 80,              // Larger depth range
      smoothingFactor: 0.2         // More responsive depth changes
    }
  };
  
  const movement3D = createMovement3D(customConfig);
  
  console.log('Custom 3D Movement System Created');
  console.log('Custom Configuration:', movement3D.getConfig());
  
  return movement3D;
}

/**
 * Example 3: Gaming-Optimized Configuration
 * Configuration optimized for gaming with responsive controls
 */
export function gamingMovement3DExample() {
  const gamingConfig = {
    sensitivity: {
      x: 1.5,    // High sensitivity for quick movements
      y: 1.3,    // High sensitivity for quick movements
      z: 1.0     // Responsive depth control
    },
    interpolation: {
      x: 0.25,   // Responsive but not jittery
      y: 0.25,   // Responsive but not jittery
      z: 0.15    // Moderate depth smoothing
    },
    boundaries: {
      x: { min: -70, max: 70 },
      y: { min: -55, max: 55 },
      z: { min: -35, max: 35 }
    }
  };
  
  return createMovement3D(gamingConfig);
}

/**
 * Example 4: Precision-Optimized Configuration
 * Configuration for precise movements (e.g., drawing, design work)
 */
export function precisionMovement3DExample() {
  const precisionConfig = {
    sensitivity: {
      x: 0.5,    // Low sensitivity for precision
      y: 0.5,    // Low sensitivity for precision
      z: 0.3     // Very controlled depth movement
    },
    interpolation: {
      x: 0.05,   // Very smooth movement
      y: 0.05,   // Very smooth movement
      z: 0.03    // Ultra-smooth depth movement
    },
    depth: {
      smoothingFactor: 0.08  // Very smooth depth changes
    }
  };
  
  return createMovement3D(precisionConfig);
}

/**
 * Example 5: Testing Hand State Processing
 * Demonstrates how to test the hand state processor independently
 */
export function testHandStateProcessing() {
  const movement3D = createMovement3D();
  
  // Mock hand landmarks (21 points as provided by TensorFlow Handpose)
  const mockLandmarks = [
    [320, 240], // 0: Wrist
    [300, 220], // 1: Thumb CMC
    [280, 200], // 2: Thumb MCP
    [260, 180], // 3: Thumb IP
    [240, 160], // 4: Thumb Tip
    [340, 200], // 5: Index MCP
    [350, 180], // 6: Index PIP
    [360, 160], // 7: Index DIP
    [370, 140], // 8: Index Tip
    [360, 200], // 9: Middle MCP
    [370, 180], // 10: Middle PIP
    [380, 160], // 11: Middle DIP
    [390, 140], // 12: Middle Tip
    [380, 200], // 13: Ring MCP
    [390, 180], // 14: Ring PIP
    [400, 160], // 15: Ring DIP
    [410, 140], // 16: Ring Tip
    [400, 200], // 17: Pinky MCP
    [410, 180], // 18: Pinky PIP
    [420, 160], // 19: Pinky DIP
    [430, 140]  // 20: Pinky Tip
  ];
  
  // Process the mock hand state
  const handState = movement3D.handProcessor.processHandState(mockLandmarks);
  
  console.log('Mock Hand State Processing Result:');
  console.log('Position 2D:', handState.position2D);
  console.log('Hand Size:', handState.handSize);
  console.log('Depth:', handState.depth);
  console.log('Confidence:', handState.confidence);
  
  return handState;
}

/**
 * Example 6: Real-time Configuration Updates
 * Shows how to update configuration during runtime
 */
export function dynamicConfigurationExample() {
  const movement3D = createMovement3D();
  
  // Function to update sensitivity based on user preference
  const updateSensitivity = (xSens, ySens, zSens) => {
    const newConfig = {
      sensitivity: {
        x: xSens,
        y: ySens,
        z: zSens
      }
    };
    movement3D.updateConfig(newConfig);
    console.log('Sensitivity updated:', newConfig.sensitivity);
  };
  
  // Function to update smoothness
  const updateSmoothness = (smoothness) => {
    const newConfig = {
      interpolation: {
        x: smoothness,
        y: smoothness,
        z: smoothness * 0.7 // Depth is typically smoother
      }
    };
    movement3D.updateConfig(newConfig);
    console.log('Smoothness updated:', newConfig.interpolation);
  };
  
  // Example usage
  console.log('Initial config:', movement3D.getConfig());
  
  // Update to high sensitivity
  updateSensitivity(1.5, 1.5, 1.2);
  
  // Update to medium smoothness
  updateSmoothness(0.15);
  
  console.log('Updated config:', movement3D.getConfig());
  
  return { movement3D, updateSensitivity, updateSmoothness };
}

/**
 * Example 7: Performance Testing
 * Demonstrates how to test performance of the 3D movement system
 */
export function performanceTestExample() {
  const movement3D = createMovement3D();
  
  // Mock 3D object for testing
  const mockObject3D = {
    position: { x: 0, y: 0, z: 0 },
    scaling: { x: 1, y: 1, z: 1 }
  };
  
  // Performance test function
  const runPerformanceTest = (iterations = 1000) => {
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      // Generate random hand state
      const mockHandState = {
        position2D: {
          x: Math.random() * 640,
          y: Math.random() * 480
        },
        handSize: 100 + Math.random() * 100,
        depth: (Math.random() - 0.5) * 60,
        confidence: Math.random()
      };
      
      // Update object position
      movement3D.updateObjectPosition(mockHandState, mockObject3D);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    const avgTime = duration / iterations;
    
    console.log(`Performance Test Results (${iterations} iterations):`);
    console.log(`Total time: ${duration.toFixed(2)}ms`);
    console.log(`Average time per update: ${avgTime.toFixed(4)}ms`);
    console.log(`Updates per second: ${(1000 / avgTime).toFixed(0)}`);
    
    return { duration, avgTime, updatesPerSecond: 1000 / avgTime };
  };
  
  return { movement3D, mockObject3D, runPerformanceTest };
}

/**
 * Example 8: Integration with Babylon.js Scene
 * Shows how to integrate the 3D movement system with a real Babylon.js scene
 */
export function babylonIntegrationExample(scene) {
  if (!scene) {
    console.warn('No Babylon.js scene provided for integration example');
    return null;
  }
  
  const movement3D = createMovement3D();
  
  // Create a test mesh in the scene
  const testBox = BABYLON.MeshBuilder.CreateBox("testBox", { size: 5 }, scene);
  const material = new BABYLON.StandardMaterial("testMaterial", scene);
  material.diffuseColor = new BABYLON.Color3(0.2, 0.8, 1.0); // Light blue
  testBox.material = material;
  
  // Function to update the test box with hand tracking
  const updateWithHandTracking = (handState) => {
    if (handState && handState.position2D) {
      const enhanced3DState = {
        position2D: handState.position2D,
        handSize: handState.handSize || 120,
        depth: handState.depth || 0,
        confidence: handState.confidence || 1.0
      };
      
      movement3D.updateObjectPosition(enhanced3DState, testBox);
    }
  };
  
  console.log('Babylon.js integration example created');
  console.log('Test box created in scene');
  
  return { movement3D, testBox, updateWithHandTracking };
}

// Export all examples for easy access
export const Movement3DExamples = {
  basic: basicMovement3DExample,
  custom: customMovement3DExample,
  gaming: gamingMovement3DExample,
  precision: precisionMovement3DExample,
  testHandState: testHandStateProcessing,
  dynamicConfig: dynamicConfigurationExample,
  performance: performanceTestExample,
  babylonIntegration: babylonIntegrationExample
};

export default Movement3DExamples;
