/**
 * Circular Motion Detection and Tracking System
 * 
 * This module detects when a finger moves in a circular path and makes
 * the 3D object follow the same circular motion pattern.
 * 
 * Features:
 * - Real-time circular motion detection
 * - Configurable sensitivity and thresholds
 * - Smooth circular path interpolation
 * - Direction detection (clockwise/counterclockwise)
 * - Radius and center point calculation
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Vector3 } from "babylonjs";

/**
 * Configuration for circular motion detection
 */
export const CircularMotionConfig = {
  // Detection parameters
  detection: {
    minPoints: 8,           // Minimum points needed to detect a circle
    maxPoints: 20,          // Maximum points to keep in history
    radiusThreshold: 30,    // Minimum radius to consider as circular motion
    maxRadiusVariation: 0.3, // Maximum allowed radius variation (30%)
    angleThreshold: Math.PI * 1.5, // Minimum angle covered to detect circle (270°)
    smoothingFactor: 0.15   // Smoothing for circular motion
  },
  
  // Visual feedback
  visual: {
    drawPath: true,         // Draw the detected circular path
    pathColor: '#FF6B6B',   // Color for the circular path
    centerColor: '#4ECDC4', // Color for the center point
    pathWidth: 3            // Width of the path line
  },
  
  // Motion parameters
  motion: {
    followSpeed: 0.2,       // How quickly object follows circular motion
    radiusScale: 0.5,       // Scale factor for 3D radius vs 2D radius
    heightVariation: 10     // Z-axis variation during circular motion
  }
};

/**
 * Circular Motion Detector
 * Analyzes finger movement to detect circular patterns
 */
export class CircularMotionDetector {
  constructor(config = CircularMotionConfig) {
    this.config = config;
    this.positionHistory = [];
    this.isCircularMotion = false;
    this.circularPath = null;
    this.lastAngle = 0;
    this.totalAngle = 0;
    this.direction = 0; // 1 for clockwise, -1 for counterclockwise
  }

  /**
   * Add a new position to the tracking history
   * @param {Object} position - {x, y} position
   */
  addPosition(position) {
    if (!position || typeof position.x !== 'number' || typeof position.y !== 'number') {
      return;
    }

    this.positionHistory.push({
      x: position.x,
      y: position.y,
      timestamp: Date.now()
    });

    // Keep only recent positions
    if (this.positionHistory.length > this.config.detection.maxPoints) {
      this.positionHistory.shift();
    }

    // Analyze for circular motion
    this.analyzeCircularMotion();
  }

  /**
   * Analyze the position history to detect circular motion
   */
  analyzeCircularMotion() {
    if (this.positionHistory.length < this.config.detection.minPoints) {
      this.isCircularMotion = false;
      return;
    }

    const circle = this.fitCircle(this.positionHistory);
    
    if (circle && this.isValidCircle(circle)) {
      this.isCircularMotion = true;
      this.circularPath = circle;
      this.calculateDirection();
    } else {
      this.isCircularMotion = false;
      this.circularPath = null;
    }
  }

  /**
   * Fit a circle to the given points using least squares method
   * @param {Array} points - Array of {x, y} points
   * @returns {Object|null} Circle parameters {centerX, centerY, radius}
   */
  fitCircle(points) {
    if (points.length < 3) return null;

    // Use simplified circle fitting algorithm
    let sumX = 0, sumY = 0, sumX2 = 0, sumY2 = 0, sumXY = 0;
    let sumX3 = 0, sumY3 = 0, sumX2Y = 0, sumXY2 = 0;

    for (const point of points) {
      const x = point.x;
      const y = point.y;
      const x2 = x * x;
      const y2 = y * y;

      sumX += x;
      sumY += y;
      sumX2 += x2;
      sumY2 += y2;
      sumXY += x * y;
      sumX3 += x2 * x;
      sumY3 += y2 * y;
      sumX2Y += x2 * y;
      sumXY2 += x * y2;
    }

    const n = points.length;
    const A = n * sumX2 - sumX * sumX;
    const B = n * sumXY - sumX * sumY;
    const C = n * sumY2 - sumY * sumY;
    const D = 0.5 * (n * sumXY2 - sumX * sumY2 + n * sumX3 - sumX * sumX2);
    const E = 0.5 * (n * sumX2Y - sumY * sumX2 + n * sumY3 - sumY * sumY2);

    const denominator = A * C - B * B;
    if (Math.abs(denominator) < 1e-10) return null;

    const centerX = (D * C - B * E) / denominator;
    const centerY = (A * E - B * D) / denominator;

    // Calculate radius as average distance from center
    let radiusSum = 0;
    for (const point of points) {
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      radiusSum += Math.sqrt(dx * dx + dy * dy);
    }
    const radius = radiusSum / points.length;

    return { centerX, centerY, radius };
  }

  /**
   * Check if the fitted circle is valid for circular motion
   * @param {Object} circle - Circle parameters
   * @returns {boolean} True if valid circular motion
   */
  isValidCircle(circle) {
    const { radiusThreshold, maxRadiusVariation, angleThreshold } = this.config.detection;

    // Check minimum radius
    if (circle.radius < radiusThreshold) {
      return false;
    }

    // Check radius consistency
    let radiusVariations = [];
    for (const point of this.positionHistory) {
      const dx = point.x - circle.centerX;
      const dy = point.y - circle.centerY;
      const distance = Math.sqrt(dx * dx + dy * dy);
      radiusVariations.push(Math.abs(distance - circle.radius) / circle.radius);
    }

    const avgVariation = radiusVariations.reduce((sum, v) => sum + v, 0) / radiusVariations.length;
    if (avgVariation > maxRadiusVariation) {
      return false;
    }

    // Check angle coverage
    const angles = this.positionHistory.map(point => {
      const dx = point.x - circle.centerX;
      const dy = point.y - circle.centerY;
      return Math.atan2(dy, dx);
    });

    let totalAngle = 0;
    for (let i = 1; i < angles.length; i++) {
      let angleDiff = angles[i] - angles[i - 1];
      // Normalize angle difference to [-π, π]
      while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
      while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
      totalAngle += Math.abs(angleDiff);
    }

    return totalAngle >= angleThreshold;
  }

  /**
   * Calculate the direction of circular motion
   */
  calculateDirection() {
    if (!this.circularPath || this.positionHistory.length < 3) {
      return;
    }

    const { centerX, centerY } = this.circularPath;
    let angleSum = 0;
    let count = 0;

    for (let i = 1; i < this.positionHistory.length; i++) {
      const prev = this.positionHistory[i - 1];
      const curr = this.positionHistory[i];

      const prevAngle = Math.atan2(prev.y - centerY, prev.x - centerX);
      const currAngle = Math.atan2(curr.y - centerY, curr.x - centerX);

      let angleDiff = currAngle - prevAngle;
      // Normalize angle difference
      while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
      while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

      angleSum += angleDiff;
      count++;
    }

    this.direction = angleSum > 0 ? 1 : -1; // 1 = counterclockwise, -1 = clockwise
    this.totalAngle = Math.abs(angleSum);
  }

  /**
   * Get the current circular motion state
   * @returns {Object} Motion state information
   */
  getMotionState() {
    return {
      isCircular: this.isCircularMotion,
      path: this.circularPath,
      direction: this.direction,
      totalAngle: this.totalAngle,
      pointCount: this.positionHistory.length
    };
  }

  /**
   * Reset the detector state
   */
  reset() {
    this.positionHistory = [];
    this.isCircularMotion = false;
    this.circularPath = null;
    this.lastAngle = 0;
    this.totalAngle = 0;
    this.direction = 0;
  }

  /**
   * Draw the detected circular path on canvas
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   */
  drawPath(ctx) {
    if (!this.config.visual.drawPath || !ctx) return;

    // Draw position history
    if (this.positionHistory.length > 1) {
      ctx.strokeStyle = this.config.visual.pathColor;
      ctx.lineWidth = this.config.visual.pathWidth;
      ctx.beginPath();
      
      const first = this.positionHistory[0];
      ctx.moveTo(first.x, first.y);
      
      for (let i = 1; i < this.positionHistory.length; i++) {
        const point = this.positionHistory[i];
        ctx.lineTo(point.x, point.y);
      }
      ctx.stroke();
    }

    // Draw detected circle
    if (this.isCircularMotion && this.circularPath) {
      const { centerX, centerY, radius } = this.circularPath;
      
      // Draw circle outline
      ctx.strokeStyle = this.config.visual.pathColor;
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.stroke();
      ctx.setLineDash([]);

      // Draw center point
      ctx.fillStyle = this.config.visual.centerColor;
      ctx.beginPath();
      ctx.arc(centerX, centerY, 4, 0, 2 * Math.PI);
      ctx.fill();

      // Draw direction indicator
      const arrowAngle = this.direction > 0 ? Math.PI / 4 : -Math.PI / 4;
      const arrowX = centerX + radius * 0.7 * Math.cos(arrowAngle);
      const arrowY = centerY + radius * 0.7 * Math.sin(arrowAngle);
      
      ctx.fillStyle = this.config.visual.centerColor;
      ctx.font = '16px Arial';
      ctx.fillText(this.direction > 0 ? '↺' : '↻', arrowX - 8, arrowY + 8);
    }
  }
}

/**
 * Circular Motion Controller
 * Handles applying circular motion to 3D objects
 */
export class CircularMotionController {
  constructor(config = CircularMotionConfig) {
    this.config = config;
    this.detector = new CircularMotionDetector(config);
    this.isActive = false;
    this.currentAngle = 0;
    this.animationSpeed = 0;
    this.originalPosition = new Vector3(0, 0, 0);
  }

  /**
   * Update with new hand position
   * @param {Object} position - Hand position {x, y}
   */
  updatePosition(position) {
    this.detector.addPosition(position);
    
    const motionState = this.detector.getMotionState();
    
    if (motionState.isCircular && !this.isActive) {
      this.startCircularMotion(motionState);
    } else if (!motionState.isCircular && this.isActive) {
      this.stopCircularMotion();
    }
  }

  /**
   * Start circular motion animation
   * @param {Object} motionState - Current motion state
   */
  startCircularMotion(motionState) {
    this.isActive = true;
    this.animationSpeed = motionState.direction * 0.02; // Adjust speed as needed
    console.log('Circular motion detected! Direction:', motionState.direction > 0 ? 'Counterclockwise' : 'Clockwise');
  }

  /**
   * Stop circular motion animation
   */
  stopCircularMotion() {
    this.isActive = false;
    this.animationSpeed = 0;
    console.log('Circular motion stopped');
  }

  /**
   * Apply circular motion to a 3D object
   * @param {Object} object3D - Babylon.js 3D object
   * @param {Object} centerPosition - Center position for circular motion
   */
  applyCircularMotion(object3D, centerPosition = null) {
    if (!this.isActive || !object3D) return;

    const motionState = this.detector.getMotionState();
    if (!motionState.isCircular || !motionState.path) return;

    // Use detected circle center or provided center
    const center = centerPosition || {
      x: motionState.path.centerX,
      y: motionState.path.centerY
    };

    // Convert 2D center to 3D coordinates (similar to existing coordinate mapping)
    const centerX = ((center.x / 640) - 0.5) * 100; // Adjust based on your coordinate system
    const centerY = ((0.5 - center.y / 480)) * 80;   // Adjust based on your coordinate system
    
    // Calculate radius in 3D space
    const radius3D = motionState.path.radius * this.config.motion.radiusScale * 0.1;

    // Update angle
    this.currentAngle += this.animationSpeed;

    // Apply circular motion
    const x = centerX + radius3D * Math.cos(this.currentAngle);
    const y = centerY + radius3D * Math.sin(this.currentAngle);
    const z = object3D.position.z + Math.sin(this.currentAngle * 2) * this.config.motion.heightVariation * 0.1;

    // Smooth interpolation to new position
    const followSpeed = this.config.motion.followSpeed;
    object3D.position.x += (x - object3D.position.x) * followSpeed;
    object3D.position.y += (y - object3D.position.y) * followSpeed;
    object3D.position.z += (z - object3D.position.z) * followSpeed * 0.5;
  }

  /**
   * Draw visual feedback on canvas
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   */
  drawFeedback(ctx) {
    this.detector.drawPath(ctx);

    // Draw status text
    if (ctx) {
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '14px Arial';
      const motionState = this.detector.getMotionState();
      
      if (motionState.isCircular) {
        ctx.fillText('🔄 Circular Motion Detected!', 10, 120);
        ctx.fillText(`Direction: ${motionState.direction > 0 ? 'Counterclockwise ↺' : 'Clockwise ↻'}`, 10, 140);
        ctx.fillText(`Radius: ${motionState.path?.radius.toFixed(0)}px`, 10, 160);
      } else if (this.detector.positionHistory.length > 0) {
        ctx.fillText(`Tracking... (${this.detector.positionHistory.length} points)`, 10, 120);
      }
    }
  }

  /**
   * Reset the controller
   */
  reset() {
    this.detector.reset();
    this.isActive = false;
    this.currentAngle = 0;
    this.animationSpeed = 0;
  }

  /**
   * Get current state
   * @returns {Object} Current controller state
   */
  getState() {
    return {
      isActive: this.isActive,
      currentAngle: this.currentAngle,
      animationSpeed: this.animationSpeed,
      motionState: this.detector.getMotionState()
    };
  }
}

/**
 * Factory function to create circular motion controller
 * @param {Object} customConfig - Custom configuration
 * @returns {CircularMotionController} Configured controller
 */
export function createCircularMotion(customConfig = {}) {
  const config = { ...CircularMotionConfig, ...customConfig };
  return new CircularMotionController(config);
}

export default CircularMotionController;
