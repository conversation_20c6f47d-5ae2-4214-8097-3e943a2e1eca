/**
 * Circular Motion Examples and Test Cases
 * 
 * This file demonstrates how to use the circular motion detection system
 * and provides examples for different use cases and configurations.
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { createCircularMotion, CircularMotionConfig } from './circularMotion.js';

/**
 * Example 1: Basic Circular Motion Setup
 * Shows how to create and use the circular motion system with default settings
 */
export function basicCircularMotionExample() {
  const circularMotion = createCircularMotion();
  
  console.log('Basic Circular Motion System Created');
  console.log('Default Configuration:', circularMotion.detector.config);
  
  // Simulate finger movement in a circle
  const simulateCircularMovement = () => {
    const centerX = 320;
    const centerY = 240;
    const radius = 50;
    const steps = 20;
    
    for (let i = 0; i < steps; i++) {
      const angle = (i / steps) * 2 * Math.PI;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      
      circularMotion.updatePosition({ x, y });
      
      // Add small delay to simulate real movement
      setTimeout(() => {
        console.log(`Step ${i + 1}: Position (${x.toFixed(1)}, ${y.toFixed(1)})`);
        if (i === steps - 1) {
          console.log('Final State:', circularMotion.getState());
        }
      }, i * 100);
    }
  };
  
  return { circularMotion, simulateCircularMovement };
}

/**
 * Example 2: Sensitive Circular Motion Detection
 * Configuration for detecting smaller, more precise circular movements
 */
export function sensitiveCircularMotionExample() {
  const sensitiveConfig = {
    detection: {
      minPoints: 6,           // Fewer points needed
      radiusThreshold: 20,    // Smaller minimum radius
      maxRadiusVariation: 0.4, // Allow more variation
      angleThreshold: Math.PI, // Only need 180° coverage
      smoothingFactor: 0.2    // More responsive
    },
    motion: {
      followSpeed: 0.3,       // Faster following
      radiusScale: 0.8,       // Larger 3D radius
      heightVariation: 15     // More Z variation
    }
  };
  
  const circularMotion = createCircularMotion(sensitiveConfig);
  
  console.log('Sensitive Circular Motion System Created');
  return circularMotion;
}

/**
 * Example 3: Precise Circular Motion Detection
 * Configuration for detecting only very precise circular movements
 */
export function preciseCircularMotionExample() {
  const preciseConfig = {
    detection: {
      minPoints: 12,          // More points needed
      radiusThreshold: 40,    // Larger minimum radius
      maxRadiusVariation: 0.15, // Very strict variation
      angleThreshold: Math.PI * 1.8, // Almost full circle needed
      smoothingFactor: 0.1    // Very smooth
    },
    motion: {
      followSpeed: 0.15,      // Slower, more precise following
      radiusScale: 0.3,       // Smaller 3D radius
      heightVariation: 5      // Minimal Z variation
    }
  };
  
  const circularMotion = createCircularMotion(preciseConfig);
  
  console.log('Precise Circular Motion System Created');
  return circularMotion;
}

/**
 * Example 4: Testing Different Circle Patterns
 * Demonstrates detection of various circular patterns
 */
export function testCircularPatternsExample() {
  const circularMotion = createCircularMotion();
  
  // Test pattern 1: Perfect circle
  const testPerfectCircle = () => {
    console.log('Testing perfect circle...');
    const centerX = 320, centerY = 240, radius = 60;
    
    for (let i = 0; i <= 20; i++) {
      const angle = (i / 20) * 2 * Math.PI;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      circularMotion.updatePosition({ x, y });
    }
    
    console.log('Perfect Circle Result:', circularMotion.getState());
    circularMotion.reset();
  };
  
  // Test pattern 2: Elliptical motion
  const testEllipse = () => {
    console.log('Testing elliptical motion...');
    const centerX = 320, centerY = 240, radiusX = 80, radiusY = 40;
    
    for (let i = 0; i <= 20; i++) {
      const angle = (i / 20) * 2 * Math.PI;
      const x = centerX + radiusX * Math.cos(angle);
      const y = centerY + radiusY * Math.sin(angle);
      circularMotion.updatePosition({ x, y });
    }
    
    console.log('Ellipse Result:', circularMotion.getState());
    circularMotion.reset();
  };
  
  // Test pattern 3: Spiral motion
  const testSpiral = () => {
    console.log('Testing spiral motion...');
    const centerX = 320, centerY = 240;
    
    for (let i = 0; i <= 20; i++) {
      const angle = (i / 20) * 3 * Math.PI; // 1.5 full rotations
      const radius = 30 + (i / 20) * 30; // Increasing radius
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      circularMotion.updatePosition({ x, y });
    }
    
    console.log('Spiral Result:', circularMotion.getState());
    circularMotion.reset();
  };
  
  return { circularMotion, testPerfectCircle, testEllipse, testSpiral };
}

/**
 * Example 5: Real-time Performance Testing
 * Tests the performance of circular motion detection
 */
export function performanceTestExample() {
  const circularMotion = createCircularMotion();
  
  const runPerformanceTest = (iterations = 1000) => {
    console.log(`Starting performance test with ${iterations} iterations...`);
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      // Generate random circular-ish movement
      const angle = (i / iterations) * 4 * Math.PI;
      const radius = 50 + Math.sin(angle * 3) * 10; // Slightly varying radius
      const x = 320 + radius * Math.cos(angle);
      const y = 240 + radius * Math.sin(angle);
      
      circularMotion.updatePosition({ x, y });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    const avgTime = duration / iterations;
    
    console.log(`Performance Test Results:`);
    console.log(`Total time: ${duration.toFixed(2)}ms`);
    console.log(`Average time per update: ${avgTime.toFixed(4)}ms`);
    console.log(`Updates per second: ${(1000 / avgTime).toFixed(0)}`);
    console.log(`Final detection state:`, circularMotion.getState());
    
    return { duration, avgTime, updatesPerSecond: 1000 / avgTime };
  };
  
  return { circularMotion, runPerformanceTest };
}

/**
 * Example 6: Integration with Canvas Drawing
 * Shows how to visualize circular motion detection on a canvas
 */
export function canvasIntegrationExample(canvas) {
  if (!canvas) {
    console.warn('No canvas provided for integration example');
    return null;
  }
  
  const ctx = canvas.getContext('2d');
  const circularMotion = createCircularMotion({
    visual: {
      drawPath: true,
      pathColor: '#FF6B6B',
      centerColor: '#4ECDC4',
      pathWidth: 3
    }
  });
  
  // Function to simulate mouse/finger movement
  const simulateMovement = (event) => {
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    circularMotion.updatePosition({ x, y });
    
    // Clear and redraw
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    circularMotion.drawFeedback(ctx);
  };
  
  // Add event listeners
  canvas.addEventListener('mousemove', simulateMovement);
  canvas.addEventListener('touchmove', (e) => {
    e.preventDefault();
    const touch = e.touches[0];
    simulateMovement(touch);
  });
  
  console.log('Canvas integration example created');
  console.log('Move mouse over canvas to test circular motion detection');
  
  return { circularMotion, ctx, simulateMovement };
}

/**
 * Example 7: Direction Detection Testing
 * Tests clockwise vs counterclockwise detection
 */
export function directionDetectionExample() {
  const circularMotion = createCircularMotion();
  
  const testClockwise = () => {
    console.log('Testing clockwise motion...');
    const centerX = 320, centerY = 240, radius = 50;
    
    // Clockwise: decreasing angle
    for (let i = 0; i <= 15; i++) {
      const angle = -((i / 15) * 2 * Math.PI); // Negative for clockwise
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      circularMotion.updatePosition({ x, y });
    }
    
    const state = circularMotion.getState();
    console.log('Clockwise Result:', {
      detected: state.isActive,
      direction: state.motionState.direction,
      directionText: state.motionState.direction < 0 ? 'Clockwise' : 'Counterclockwise'
    });
    
    circularMotion.reset();
  };
  
  const testCounterclockwise = () => {
    console.log('Testing counterclockwise motion...');
    const centerX = 320, centerY = 240, radius = 50;
    
    // Counterclockwise: increasing angle
    for (let i = 0; i <= 15; i++) {
      const angle = (i / 15) * 2 * Math.PI; // Positive for counterclockwise
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      circularMotion.updatePosition({ x, y });
    }
    
    const state = circularMotion.getState();
    console.log('Counterclockwise Result:', {
      detected: state.isActive,
      direction: state.motionState.direction,
      directionText: state.motionState.direction > 0 ? 'Counterclockwise' : 'Clockwise'
    });
    
    circularMotion.reset();
  };
  
  return { circularMotion, testClockwise, testCounterclockwise };
}

/**
 * Example 8: Configuration Comparison
 * Compares different configurations side by side
 */
export function configurationComparisonExample() {
  const configs = {
    default: CircularMotionConfig,
    sensitive: {
      ...CircularMotionConfig,
      detection: {
        ...CircularMotionConfig.detection,
        minPoints: 6,
        radiusThreshold: 20,
        angleThreshold: Math.PI
      }
    },
    precise: {
      ...CircularMotionConfig,
      detection: {
        ...CircularMotionConfig.detection,
        minPoints: 15,
        radiusThreshold: 60,
        maxRadiusVariation: 0.1
      }
    }
  };
  
  const controllers = {};
  Object.keys(configs).forEach(key => {
    controllers[key] = createCircularMotion(configs[key]);
  });
  
  const testAllConfigurations = () => {
    console.log('Testing all configurations with same input...');
    
    // Generate test circle
    const centerX = 320, centerY = 240, radius = 45;
    const testPoints = [];
    
    for (let i = 0; i <= 12; i++) {
      const angle = (i / 12) * 2 * Math.PI;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      testPoints.push({ x, y });
    }
    
    // Test each configuration
    Object.keys(controllers).forEach(configName => {
      const controller = controllers[configName];
      controller.reset();
      
      testPoints.forEach(point => {
        controller.updatePosition(point);
      });
      
      const state = controller.getState();
      console.log(`${configName} configuration:`, {
        detected: state.isActive,
        radius: state.motionState.path?.radius?.toFixed(1),
        direction: state.motionState.direction > 0 ? 'CCW' : 'CW'
      });
    });
  };
  
  return { controllers, configs, testAllConfigurations };
}

// Export all examples for easy access
export const CircularMotionExamples = {
  basic: basicCircularMotionExample,
  sensitive: sensitiveCircularMotionExample,
  precise: preciseCircularMotionExample,
  patterns: testCircularPatternsExample,
  performance: performanceTestExample,
  canvas: canvasIntegrationExample,
  direction: directionDetectionExample,
  comparison: configurationComparisonExample
};

export default CircularMotionExamples;
