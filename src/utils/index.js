const fingerJoints = {
  thumb: [0,1,2,3,4],
  indexFinger: [0,5,6,7,8],
  middleFinger: [0,9,10,11,12],
  ringFinger: [0,13,14,15,16],
  pinky: [0,17,18,19,20]
}
// Note: fingers and maxCloseHandDistance variables removed as they were unused
// They can be re-added if needed for future gesture recognition features
/**
 * Calculate hand size for depth detection
 * @param {Array} landmarks - Hand landmark points
 * @returns {number} Hand size in pixels
 */
const calculateHandSize = (landmarks) => {
  // Primary measurement: wrist to middle finger tip
  const wrist = landmarks[0];
  const middleFingerTip = landmarks[12];
  const primaryDistance = Math.sqrt(
    Math.pow(middleFingerTip[0] - wrist[0], 2) +
    Math.pow(middleFingerTip[1] - wrist[1], 2)
  );

  // Secondary measurement: thumb to pinky span
  const thumbTip = landmarks[4];
  const pinkyTip = landmarks[20];
  const spanDistance = Math.sqrt(
    Math.pow(pinkyTip[0] - thumbTip[0], 2) +
    Math.pow(pinkyTip[1] - thumbTip[1], 2)
  );

  // Weighted average for stability
  return (primaryDistance * 0.7) + (spanDistance * 0.3);
};

/**
 * Enhanced hand detection with 3D depth information and circular motion tracking
 * @param {Array} predictions - TensorFlow hand predictions
 * @param {CanvasRenderingContext2D} ctx - Canvas context for drawing
 * @param {boolean} enable3D - Whether to include 3D depth data
 * @param {Object} circularMotionController - Optional circular motion controller
 * @returns {Object} Enhanced hand state with optional 3D data and circular motion
 */
export const drawHand = (predictions, ctx, enable3D = false, circularMotionController = null) => {
  let handState = {
    isPinched: false,
    position: { x: 0, y: 0 },
    fingerSpread: 0,
    isTracking: false,
    // Enhanced 3D properties
    handSize: 0,
    depth: 0,
    landmarks: null,
    confidence: 0,
    // Circular motion properties
    circularMotion: null
  };

  if (predictions.length > 0) {
    const landmarks = predictions[0].landmarks;
    handState.isTracking = true;
    handState.landmarks = landmarks;
    handState.confidence = predictions[0].handInViewConfidence || 1.0;

    // Draw hand points and connections
    for (let i = 0; i < landmarks.length; i++) {
      const x = landmarks[i][0];
      const y = landmarks[i][1];

      // Draw points with depth-based coloring if 3D mode is enabled
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, 3 * Math.PI);

      if (enable3D) {
        // Color points based on depth (closer = brighter green)
        const handSize = calculateHandSize(landmarks);
        const intensity = Math.max(0.3, Math.min(1.0, handSize / 150));
        ctx.fillStyle = `rgb(0, ${Math.floor(255 * intensity)}, 0)`;
      } else {
        ctx.fillStyle = '#00FF00';
      }
      ctx.fill();
    }

    // Draw finger connections
    for (const finger of Object.keys(fingerJoints)) {
      const points = fingerJoints[finger];
      for (let i = 0; i < points.length - 1; i++) {
        const firstPoint = landmarks[points[i]];
        const secondPoint = landmarks[points[i + 1]];

        // Draw lines
        ctx.beginPath();
        ctx.moveTo(firstPoint[0], firstPoint[1]);
        ctx.lineTo(secondPoint[0], secondPoint[1]);
        ctx.strokeStyle = enable3D ? '#00FF88' : '#00FF00';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    }

    // Calculate hand position (using index finger base as reference)
    handState.position = {
      x: landmarks[5][0], // Index finger base
      y: landmarks[5][1]
    };

    // Update circular motion tracking if controller is provided
    if (circularMotionController) {
      // Use index finger tip for more precise circular motion tracking
      const indexTip = landmarks[8];
      circularMotionController.updatePosition({
        x: indexTip[0],
        y: indexTip[1]
      });
      handState.circularMotion = circularMotionController.getState();
    }

    // Calculate hand size and depth for 3D mode
    if (enable3D) {
      handState.handSize = calculateHandSize(landmarks);

      // Simple depth calculation based on hand size
      // Larger hand = closer (positive Z), smaller hand = farther (negative Z)
      const baseSize = 120; // Reference hand size at neutral depth
      const depthSensitivity = 0.3;
      handState.depth = (handState.handSize - baseSize) * depthSensitivity;
    }

    // Check for pinch (distance between thumb tip and index tip)
    const thumbTip = landmarks[4];
    const indexTip = landmarks[8];
    const pinchDistance = Math.sqrt(
      Math.pow(thumbTip[0] - indexTip[0], 2) +
      Math.pow(thumbTip[1] - indexTip[1], 2)
    );

    handState.isPinched = pinchDistance < 30; // Adjust threshold as needed

    // Calculate finger spread only when pinched
    if (handState.isPinched) {
      const thumbBase = landmarks[2];
      const pinkyBase = landmarks[17];
      handState.fingerSpread = Math.sqrt(
        Math.pow(thumbBase[0] - pinkyBase[0], 2) +
        Math.pow(thumbBase[1] - pinkyBase[1], 2)
      );
    }

    // Draw depth indicator if 3D mode is enabled
    if (enable3D && ctx) {
      const depthBarX = 10;
      const depthBarY = 10;
      const depthBarWidth = 20;
      const depthBarHeight = 100;

      // Draw depth bar background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(depthBarX, depthBarY, depthBarWidth, depthBarHeight);

      // Draw depth indicator
      const depthNormalized = Math.max(0, Math.min(1, (handState.handSize - 80) / 120));
      const indicatorHeight = depthNormalized * depthBarHeight;
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(depthBarX, depthBarY + depthBarHeight - indicatorHeight, depthBarWidth, indicatorHeight);

      // Draw depth text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px Arial';
      ctx.fillText(`Depth: ${handState.depth.toFixed(1)}`, depthBarX + 30, depthBarY + 15);
      ctx.fillText(`Size: ${handState.handSize.toFixed(0)}`, depthBarX + 30, depthBarY + 30);
    }

    // Draw circular motion feedback if controller is provided
    if (circularMotionController) {
      circularMotionController.drawFeedback(ctx);
    }
  }

  return handState;
}
