// Import gesture recognition
import { gestureRecognizer } from './gestureRecognition.js';

const fingerJoints = {
  thumb: [0,1,2,3,4],
  indexFinger: [0,5,6,7,8],
  middleFinger: [0,9,10,11,12],
  ringFinger: [0,13,14,15,16],
  pinky: [0,17,18,19,20]
}
// Note: fingers and maxCloseHandDistance variables removed as they were unused
// They can be re-added if needed for future gesture recognition features
/**
 * Calculate hand size for depth detection
 * @param {Array} landmarks - Hand landmark points
 * @returns {number} Hand size in pixels
 */
const calculateHandSize = (landmarks) => {
  // Primary measurement: wrist to middle finger tip
  const wrist = landmarks[0];
  const middleFingerTip = landmarks[12];
  const primaryDistance = Math.sqrt(
    Math.pow(middleFingerTip[0] - wrist[0], 2) +
    Math.pow(middleFingerTip[1] - wrist[1], 2)
  );

  // Secondary measurement: thumb to pinky span
  const thumbTip = landmarks[4];
  const pinkyTip = landmarks[20];
  const spanDistance = Math.sqrt(
    Math.pow(pinkyTip[0] - thumbTip[0], 2) +
    Math.pow(pinkyTip[1] - thumbTip[1], 2)
  );

  // Weighted average for stability
  return (primaryDistance * 0.7) + (spanDistance * 0.3);
};

/**
 * Enhanced hand detection with 3D depth information and circular motion tracking
 * @param {Array} predictions - TensorFlow hand predictions
 * @param {CanvasRenderingContext2D} ctx - Canvas context for drawing
 * @param {boolean} enable3D - Whether to include 3D depth data
 * @param {Object} circularMotionController - Optional circular motion controller
 * @returns {Object} Enhanced hand state with optional 3D data and circular motion
 */
export const drawHand = (predictions, ctx, enable3D = false, circularMotionController = null) => {
  // Process detection with AI enhancements
  const detectionResult = enhancedDetector.processDetection(predictions);

  let handState = {
    isPinched: false,
    position: { x: 0, y: 0 },
    fingerSpread: 0,
    isTracking: detectionResult.isValid,
    // Enhanced 3D properties
    handSize: 0,
    depth: 0,
    landmarks: null,
    confidence: detectionResult.confidence,
    // Circular motion properties
    circularMotion: null,
    // AI enhancement properties
    detectionStatus: detectionResult.status,
    smoothedPosition: detectionResult.smoothedPosition,
    // Gesture recognition properties
    recognizedGesture: null,
    gestureConfidence: 0,
    gestureAction: null
  };

  if (detectionResult.isValid && detectionResult.landmarks) {
    const landmarks = detectionResult.landmarks;
    handState.landmarks = landmarks;

    // Perform gesture recognition
    const gestureResult = gestureRecognizer.recognizeGesture(landmarks);
    handState.recognizedGesture = gestureResult.gesture;
    handState.gestureConfidence = gestureResult.confidence;
    handState.gestureAction = gestureResult.action;

    // Draw hand points and connections
    for (let i = 0; i < landmarks.length; i++) {
      const x = landmarks[i][0];
      const y = landmarks[i][1];

      // Draw points with depth-based coloring if 3D mode is enabled
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, 3 * Math.PI);

      if (enable3D) {
        // Color points based on depth (closer = brighter green)
        const handSize = calculateHandSize(landmarks);
        const intensity = Math.max(0.3, Math.min(1.0, handSize / 150));
        ctx.fillStyle = `rgb(0, ${Math.floor(255 * intensity)}, 0)`;
      } else {
        ctx.fillStyle = '#00FF00';
      }
      ctx.fill();
    }

    // Draw finger connections
    for (const finger of Object.keys(fingerJoints)) {
      const points = fingerJoints[finger];
      for (let i = 0; i < points.length - 1; i++) {
        const firstPoint = landmarks[points[i]];
        const secondPoint = landmarks[points[i + 1]];

        // Draw lines
        ctx.beginPath();
        ctx.moveTo(firstPoint[0], firstPoint[1]);
        ctx.lineTo(secondPoint[0], secondPoint[1]);
        ctx.strokeStyle = enable3D ? '#00FF88' : '#00FF00';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    }

    // Use smoothed position if available, otherwise use raw position
    handState.position = detectionResult.smoothedPosition || {
      x: landmarks[5][0], // Index finger base
      y: landmarks[5][1]
    };

    // Add point to path tracker
    handPathTracker.addPoint(handState.position);

    // Update circular motion tracking if controller is provided
    if (circularMotionController) {
      // Use index finger tip for more precise circular motion tracking
      const indexTip = landmarks[8];
      circularMotionController.updatePosition({
        x: indexTip[0],
        y: indexTip[1]
      });
      handState.circularMotion = circularMotionController.getState();
    }

    // Calculate hand size and depth for 3D mode
    if (enable3D) {
      handState.handSize = calculateHandSize(landmarks);

      // Simple depth calculation based on hand size
      // Larger hand = closer (positive Z), smaller hand = farther (negative Z)
      const baseSize = 120; // Reference hand size at neutral depth
      const depthSensitivity = 0.3;
      handState.depth = (handState.handSize - baseSize) * depthSensitivity;
    }

    // Check for pinch (distance between thumb tip and index tip)
    const thumbTip = landmarks[4];
    const indexTip = landmarks[8];
    const pinchDistance = Math.sqrt(
      Math.pow(thumbTip[0] - indexTip[0], 2) +
      Math.pow(thumbTip[1] - indexTip[1], 2)
    );

    handState.isPinched = pinchDistance < 30; // Adjust threshold as needed

    // Calculate finger spread only when pinched
    if (handState.isPinched) {
      const thumbBase = landmarks[2];
      const pinkyBase = landmarks[17];
      handState.fingerSpread = Math.sqrt(
        Math.pow(thumbBase[0] - pinkyBase[0], 2) +
        Math.pow(thumbBase[1] - pinkyBase[1], 2)
      );
    }

    // Draw depth indicator if 3D mode is enabled
    if (enable3D && ctx) {
      const depthBarX = 10;
      const depthBarY = 10;
      const depthBarWidth = 20;
      const depthBarHeight = 100;

      // Draw depth bar background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(depthBarX, depthBarY, depthBarWidth, depthBarHeight);

      // Draw depth indicator
      const depthNormalized = Math.max(0, Math.min(1, (handState.handSize - 80) / 120));
      const indicatorHeight = depthNormalized * depthBarHeight;
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(depthBarX, depthBarY + depthBarHeight - indicatorHeight, depthBarWidth, indicatorHeight);

      // Draw depth text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px Arial';
      ctx.fillText(`Depth: ${handState.depth.toFixed(1)}`, depthBarX + 30, depthBarY + 15);
      ctx.fillText(`Size: ${handState.handSize.toFixed(0)}`, depthBarX + 30, depthBarY + 30);
    }

    // Draw circular motion feedback if controller is provided
    if (circularMotionController) {
      circularMotionController.drawFeedback(ctx);
    }
  }

  // Draw hand path trail
  handPathTracker.updateAndDraw(ctx);

  // Draw enhanced UI information
  drawEnhancedUI(ctx, handState, enable3D);

  return handState;
}

/**
 * Draw enhanced UI information on the canvas
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Object} handState - Current hand state
 * @param {boolean} enable3D - Whether 3D mode is enabled
 */
function drawEnhancedUI(ctx, handState, enable3D) {
  if (!ctx) return;

  ctx.save();

  // Draw detection status
  const statusY = enable3D ? 200 : 120;
  ctx.fillStyle = handState.isTracking ? '#00FF00' : '#FF6B6B';
  ctx.font = 'bold 14px Arial';
  ctx.fillText(`Status: ${handState.detectionStatus}`, 10, statusY);

  // Draw confidence meter
  const confidenceY = statusY + 20;
  ctx.fillStyle = '#FFFFFF';
  ctx.font = '12px Arial';
  ctx.fillText(`Confidence: ${(handState.confidence * 100).toFixed(1)}%`, 10, confidenceY);

  // Draw confidence bar
  const barWidth = 100;
  const barHeight = 8;
  const barX = 120;
  const barY = confidenceY - 12;

  // Background
  ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
  ctx.fillRect(barX, barY, barWidth, barHeight);

  // Confidence level
  const confidenceWidth = barWidth * handState.confidence;
  const confidenceColor = handState.confidence > 0.8 ? '#00FF00' :
                         handState.confidence > 0.5 ? '#FFFF00' : '#FF6B6B';
  ctx.fillStyle = confidenceColor;
  ctx.fillRect(barX, barY, confidenceWidth, barHeight);

  // Draw gesture recognition info
  if (handState.recognizedGesture) {
    const gestureY = confidenceY + 25;
    ctx.fillStyle = '#FF6B6B';
    ctx.font = 'bold 12px Arial';
    ctx.fillText(`Gesture: ${handState.recognizedGesture}`, 10, gestureY);

    const gestureConfY = gestureY + 15;
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '11px Arial';
    ctx.fillText(`Confidence: ${(handState.gestureConfidence * 100).toFixed(0)}%`, 10, gestureConfY);
  }

  // Draw FPS counter (estimated)
  const fpsY = handState.recognizedGesture ? confidenceY + 55 : confidenceY + 25;
  ctx.fillStyle = '#FFFFFF';
  ctx.font = '12px Arial';
  const fps = Math.round(1000 / 50); // Approximate based on detection interval
  ctx.fillText(`FPS: ~${fps}`, 10, fpsY);

  // Draw path trail toggle hint
  const hintY = fpsY + 25;
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
  ctx.font = '11px Arial';
  ctx.fillText('Press T to toggle trail', 10, hintY);

  ctx.restore();
}

// Global instances
const handPathTracker = new HandPathTracker();
const enhancedDetector = new EnhancedHandDetector();

// Export path tracker for external control
export { handPathTracker };
