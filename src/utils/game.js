import {
  Engine,
  HDRCubeTexture,
  Scene,
  UniversalCamera,
  HemisphericLight,
  Vector3,
  Color3,
  StandardMaterial,
  Texture,
  AssetsManager,
  Mesh,
  ActionManager
 } from "babylonjs"
import GUI from 'babylonjs-gui';
import moonlitGolfHDRUrl from './moonlit_golf_2k.hdr?url';
import { createMovement3D } from './freeMovement.js';
import { createCircularMotion } from './circularMotion.js';


export default class Game{

  constructor(canvas, options = {}){
    this.canvas = canvas;
    this.engine = new Engine(this.canvas, true);
    this.scene = new Scene(this.engine);
    this.scene.clearColor = new Color3.FromHexString("#888888");

    // Initialize keyboard input
    this.scene.actionManager = new BABYLON.ActionManager(this.scene);

    // Movement system configuration
    this.movementMode = options.movementMode || '2D'; // '2D', '3D', or 'circular'
    this.movement3D = createMovement3D(options.movement3DConfig);
    this.circularMotion = createCircularMotion(options.circularMotionConfig);

    this.createScene();
    this.runEngineLoop();
    this.boxMinSize = 3;
    this.boxMaxSize = 8;
    this.moveSpeed = 0.5;
    this.lastHandPosition = null;
    this.isScalingMode = false;
    this.initialFingerSpread = 0;
    this.initialScale = this.boxMinSize;

    // Movement state
    this.lastHandState3D = null;
    this.isCircularMode = false;
  }

  createScene(){
    this.camera = new UniversalCamera("UniversalCamera", new Vector3(-90, -10, -10), this.scene);
    this.camera.rotation.x = -3;
    this.camera.setTarget(Vector3.Zero());
    this.camera.attachControl(this.canvas, true);
    this.camera.keysLeft= [65];
    this.camera.keysRight=[68];
    this.camera.keysUp = [87];
    this.camera.keysDown = [83];

    //add light
    this.addLighting();
    // Skybox
    this.generateSkybox();
    
    this.generateMeshes();
    // this.generateModels();
    return this.scene;
  }
  runEngineLoop(){
    this.engine.runRenderLoop(() => {
      // Just render the scene - movement is handled by moveBox method
      this.scene.render();
    });
  }
  getGameInstance(){
    return this.canvas;
  }

  addLighting(){
    var light = new HemisphericLight("hemiLight", new Vector3(-1, 1, 0), this.scene);
    light.diffuse = new Color3(1, 0, 0);
  }

  generateSkybox(){
    this.skybox = BABYLON.MeshBuilder.CreateBox("skyBox", {size:300.0}, this.scene);
    // skybox.checkCollisions = true;
    this.skybox.ellipsoid = new Vector3(3,3,3);
    
    this.skyboxMaterial = new StandardMaterial("skyBox", this.scene);
    this.skyboxMaterial.backFaceCulling = false;
    this.skyboxMaterial.reflectionTexture = new HDRCubeTexture(moonlitGolfHDRUrl, this.scene, 512, false, true, false, true);
    this.skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
    this.skyboxMaterial.disableLighting = true;

    this.skybox.material = this.skyboxMaterial;			
  }

  generateModels(){
    this.assetsLoader = new AssetsManager(this.scene);
    this.planeModel = this.assetsLoader.addMeshTask("ship", "", "models/", "ship.obj");

    this.assetsLoader.onFinish = (tasks) => {
      console.log(this.scene.meshes.length);
      this.runEngineLoop();
      console.log('assets loaded');
    }
    this.assetsLoader.load();
  }

  generateMeshes(){
    this.box1 = new Mesh.CreateBox("Box1", 5, this.scene);
    
    const coralMaterial = new StandardMaterial("coral_mat", this.scene);
    coralMaterial.diffuseColor = new Color3.FromHexString("#FF7F50");
    this.box1.material = coralMaterial;

    // Set initial position to center of scene
    this.box1.position.x = 0;
    this.box1.position.y = 0;
    this.box1.position.z = 0;

    this.scene.gravity = new Vector3(0, -9.81, 0);
    this.scene.collisionsEnabled = true;
  }

  /**
   * Switch between movement modes
   * @param {string} mode - '2D', '3D', or 'circular'
   */
  setMovementMode(mode) {
    if (mode === '2D' || mode === '3D' || mode === 'circular') {
      this.movementMode = mode;
      this.isCircularMode = (mode === 'circular');

      // Reset circular motion when switching modes
      if (mode !== 'circular') {
        this.circularMotion.reset();
      }

      console.log(`Movement mode switched to: ${mode}`);
    }
  }

  /**
   * Get current movement mode
   * @returns {string} Current movement mode
   */
  getMovementMode() {
    return this.movementMode;
  }

  /**
   * Update 3D movement configuration
   * @param {Object} config - New configuration values
   */
  updateMovement3DConfig(config) {
    this.movement3D.updateConfig(config);
  }

  /**
   * Get circular motion controller for external access
   * @returns {Object} Circular motion controller
   */
  getCircularMotionController() {
    return this.circularMotion;
  }

  /**
   * Toggle circular motion mode
   */
  toggleCircularMode() {
    this.isCircularMode = !this.isCircularMode;
    if (!this.isCircularMode) {
      this.circularMotion.reset();
    }
    console.log(`Circular motion mode: ${this.isCircularMode ? 'ON' : 'OFF'}`);
  }

  /**
   * Enhanced moveBox method with 3D movement support
   * @param {Object} handState - Hand state from detection
   */
  moveBox(handState) {
    if (!handState || !handState.isTracking || !this.box1) return;

    // Initialize last position if not set
    if (!this.lastHandPosition && handState.isTracking) {
      this.lastHandPosition = { ...handState.position };
      return;
    }

    // Handle scaling mode (works in both 2D and 3D modes)
    if (handState.isPinched) {
      if (!this.isScalingMode) {
        // Enter scaling mode
        this.isScalingMode = true;
        this.initialFingerSpread = handState.fingerSpread;
        this.initialScale = this.box1.scaling.x;
      } else {
        // Update scale
        const scaleDelta = (handState.fingerSpread - this.initialFingerSpread) / 100;
        const newScale = Math.max(
          this.boxMinSize,
          Math.min(this.boxMaxSize, this.initialScale + scaleDelta)
        );

        this.box1.scaling.x = newScale;
        this.box1.scaling.y = newScale;
        this.box1.scaling.z = newScale;
      }
    } else {
      // Exit scaling mode
      if (this.isScalingMode) {
        this.isScalingMode = false;
        this.lastHandPosition = { ...handState.position };
      }

      // Handle movement only when not in scaling mode
      if (!this.isScalingMode) {
        if (this.movementMode === '3D') {
          this.moveBox3D(handState);
        } else if (this.movementMode === 'circular' || this.isCircularMode) {
          this.moveBoxCircular(handState);
        } else {
          this.moveBox2D(handState);
        }
      }
    }

    // Apply circular motion if active (works in any mode)
    if (this.isCircularMode || this.movementMode === 'circular') {
      this.circularMotion.applyCircularMotion(this.box1);
    }

    // Update last position
    this.lastHandPosition = { ...handState.position };
  }

  /**
   * Enhanced 2D movement logic with physics
   * @param {Object} handState - Hand state from detection
   */
  moveBox2D(handState) {
    // Map webcam coordinates (640x480) to scene coordinates
    const sceneWidth = 100; // Total width of scene from -50 to 50
    const sceneHeight = 80; // Total height of scene from -40 to 40

    // Calculate mapped coordinates
    const mappedX = ((handState.position.x / 640) * sceneWidth) - (sceneWidth / 2);
    const mappedY = ((1 - handState.position.y / 480) * sceneHeight) - (sceneHeight / 2);

    // Calculate box boundaries (considering box size)
    const boxSize = this.box1.scaling.x * 5; // 5 is the original box size
    const boundaryOffset = boxSize / 2;

    // Apply boundaries
    const targetX = Math.max(
      -(sceneWidth/2) + boundaryOffset,
      Math.min(sceneWidth/2 - boundaryOffset, mappedX)
    );

    const targetY = Math.max(
      -(sceneHeight/2) + boundaryOffset,
      Math.min(sceneHeight/2 - boundaryOffset, mappedY)
    );

    // Create target position vector
    const targetPosition = new Vector3(targetX, targetY, this.box1.position.z);

    // Apply physics-based movement if physics controller is available
    if (this.physicsController) {
      const newPosition = this.physicsController.updatePhysics(
        this.box1.position.clone(),
        targetPosition
      );

      this.box1.position.x = newPosition.x;
      this.box1.position.y = newPosition.y;
    } else {
      // Fallback to simple lerp if physics controller is not available
      const lerpFactor = 0.1;
      this.box1.position.x += (targetX - this.box1.position.x) * lerpFactor;
      this.box1.position.y += (targetY - this.box1.position.y) * lerpFactor;
    }
  }

  /**
   * Enhanced 3D movement logic using the new movement system
   * @param {Object} handState - Enhanced hand state with 3D data
   */
  moveBox3D(handState) {
    // Check if hand state has 3D data (depth, handSize)
    if (handState.handSize !== undefined && handState.depth !== undefined) {
      // Create enhanced hand state for 3D movement
      const handState3D = {
        position2D: handState.position,
        handSize: handState.handSize,
        depth: handState.depth,
        landmarks: handState.landmarks,
        confidence: handState.confidence || 1.0
      };

      // Use the 3D movement controller
      this.movement3D.updateObjectPosition(handState3D, this.box1);
      this.lastHandState3D = handState3D;
    } else {
      // Fallback to 2D movement if 3D data is not available
      console.warn('3D movement mode enabled but no 3D data available, falling back to 2D');
      this.moveBox2D(handState);
    }
  }

  /**
   * Circular movement logic with fallback to normal movement
   * @param {Object} handState - Hand state from detection
   */
  moveBoxCircular(handState) {
    // Check if circular motion is detected and active
    if (handState.circularMotion && handState.circularMotion.isActive) {
      // Circular motion is being applied automatically in the main moveBox method
      // Just ensure we don't interfere with it
      return;
    } else {
      // No circular motion detected, use normal movement as fallback
      if (this.movementMode === '3D' || (handState.handSize !== undefined && handState.depth !== undefined)) {
        this.moveBox3D(handState);
      } else {
        this.moveBox2D(handState);
      }
    }
  }
}
