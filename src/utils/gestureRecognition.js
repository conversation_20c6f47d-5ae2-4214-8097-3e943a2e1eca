/**
 * Advanced Gesture Recognition System
 * 
 * This module provides enhanced gesture recognition capabilities including:
 * - Multi-finger gesture detection
 * - Dynamic gesture patterns
 * - Confidence scoring for gestures
 * - Customizable gesture definitions
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Gesture Recognition Engine
 */
export class GestureRecognizer {
  constructor() {
    this.gestures = new Map();
    this.gestureHistory = [];
    this.maxHistorySize = 10;
    this.confidenceThreshold = 0.7;
    
    // Initialize default gestures
    this.initializeDefaultGestures();
  }

  /**
   * Initialize default gesture patterns
   */
  initializeDefaultGestures() {
    // Peace sign (V gesture)
    this.addGesture('peace', {
      name: 'Peace Sign',
      description: 'Index and middle finger extended',
      detector: this.detectPeaceSign.bind(this),
      action: 'toggle_mode'
    });

    // Thumbs up
    this.addGesture('thumbs_up', {
      name: 'Thumbs Up',
      description: 'Thumb extended upward',
      detector: this.detectThumbsUp.bind(this),
      action: 'increase_speed'
    });

    // Thumbs down
    this.addGesture('thumbs_down', {
      name: 'Thumbs Down',
      description: 'Thumb extended downward',
      detector: this.detectThumbsDown.bind(this),
      action: 'decrease_speed'
    });

    // Open palm
    this.addGesture('open_palm', {
      name: 'Open Palm',
      description: 'All fingers extended',
      detector: this.detectOpenPalm.bind(this),
      action: 'reset_position'
    });

    // Fist
    this.addGesture('fist', {
      name: 'Fist',
      description: 'All fingers closed',
      detector: this.detectFist.bind(this),
      action: 'pause_movement'
    });
  }

  /**
   * Add a custom gesture
   * @param {string} id - Gesture identifier
   * @param {Object} gestureConfig - Gesture configuration
   */
  addGesture(id, gestureConfig) {
    this.gestures.set(id, gestureConfig);
  }

  /**
   * Recognize gestures from hand landmarks
   * @param {Array} landmarks - Hand landmark points
   * @returns {Object} Recognition result
   */
  recognizeGesture(landmarks) {
    if (!landmarks || landmarks.length === 0) {
      return { gesture: null, confidence: 0, action: null };
    }

    let bestMatch = { gesture: null, confidence: 0, action: null };

    // Test each registered gesture
    for (const [id, gestureConfig] of this.gestures) {
      const confidence = gestureConfig.detector(landmarks);
      
      if (confidence > bestMatch.confidence && confidence >= this.confidenceThreshold) {
        bestMatch = {
          gesture: gestureConfig.name,
          gestureId: id,
          confidence: confidence,
          action: gestureConfig.action,
          description: gestureConfig.description
        };
      }
    }

    // Add to history
    this.addToHistory(bestMatch);

    return bestMatch;
  }

  /**
   * Detect peace sign gesture
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Confidence score (0-1)
   */
  detectPeaceSign(landmarks) {
    // Check if index and middle fingers are extended
    const indexExtended = this.isFingerExtended(landmarks, 'index');
    const middleExtended = this.isFingerExtended(landmarks, 'middle');
    const ringClosed = !this.isFingerExtended(landmarks, 'ring');
    const pinkyClosed = !this.isFingerExtended(landmarks, 'pinky');
    
    if (indexExtended && middleExtended && ringClosed && pinkyClosed) {
      // Check if fingers are spread apart (V shape)
      const indexTip = landmarks[8];
      const middleTip = landmarks[12];
      const distance = this.calculateDistance(indexTip, middleTip);
      
      // Normalize distance based on hand size
      const handSize = this.calculateHandSize(landmarks);
      const normalizedDistance = distance / handSize;
      
      // V shape should have reasonable spread
      if (normalizedDistance > 0.3 && normalizedDistance < 0.8) {
        return 0.9;
      }
    }
    
    return 0.0;
  }

  /**
   * Detect thumbs up gesture
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Confidence score (0-1)
   */
  detectThumbsUp(landmarks) {
    const thumbExtended = this.isFingerExtended(landmarks, 'thumb');
    const othersClosed = ['index', 'middle', 'ring', 'pinky'].every(
      finger => !this.isFingerExtended(landmarks, finger)
    );
    
    if (thumbExtended && othersClosed) {
      // Check thumb orientation (should point upward)
      const thumbTip = landmarks[4];
      const thumbBase = landmarks[2];
      const isUpward = thumbTip[1] < thumbBase[1]; // Y decreases upward
      
      return isUpward ? 0.85 : 0.3;
    }
    
    return 0.0;
  }

  /**
   * Detect thumbs down gesture
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Confidence score (0-1)
   */
  detectThumbsDown(landmarks) {
    const thumbExtended = this.isFingerExtended(landmarks, 'thumb');
    const othersClosed = ['index', 'middle', 'ring', 'pinky'].every(
      finger => !this.isFingerExtended(landmarks, finger)
    );
    
    if (thumbExtended && othersClosed) {
      // Check thumb orientation (should point downward)
      const thumbTip = landmarks[4];
      const thumbBase = landmarks[2];
      const isDownward = thumbTip[1] > thumbBase[1]; // Y increases downward
      
      return isDownward ? 0.85 : 0.3;
    }
    
    return 0.0;
  }

  /**
   * Detect open palm gesture
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Confidence score (0-1)
   */
  detectOpenPalm(landmarks) {
    const allExtended = ['thumb', 'index', 'middle', 'ring', 'pinky'].every(
      finger => this.isFingerExtended(landmarks, finger)
    );
    
    if (allExtended) {
      // Check if fingers are reasonably spread
      const fingerTips = [landmarks[4], landmarks[8], landmarks[12], landmarks[16], landmarks[20]];
      const spreadScore = this.calculateFingerSpread(fingerTips);
      
      return Math.min(0.9, 0.6 + spreadScore * 0.3);
    }
    
    return 0.0;
  }

  /**
   * Detect fist gesture
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Confidence score (0-1)
   */
  detectFist(landmarks) {
    const allClosed = ['index', 'middle', 'ring', 'pinky'].every(
      finger => !this.isFingerExtended(landmarks, finger)
    );
    
    // Thumb can be either closed or tucked
    return allClosed ? 0.85 : 0.0;
  }

  /**
   * Check if a finger is extended
   * @param {Array} landmarks - Hand landmarks
   * @param {string} finger - Finger name
   * @returns {boolean} True if finger is extended
   */
  isFingerExtended(landmarks, finger) {
    const fingerIndices = {
      thumb: [2, 3, 4],
      index: [6, 7, 8],
      middle: [10, 11, 12],
      ring: [14, 15, 16],
      pinky: [18, 19, 20]
    };
    
    const indices = fingerIndices[finger];
    if (!indices) return false;
    
    // For thumb, check different angle due to its orientation
    if (finger === 'thumb') {
      const tip = landmarks[indices[2]];
      const middle = landmarks[indices[1]];
      const base = landmarks[indices[0]];
      
      const tipToMiddle = this.calculateDistance(tip, middle);
      const middleToBase = this.calculateDistance(middle, base);
      
      return tipToMiddle > middleToBase * 0.8;
    }
    
    // For other fingers, check if tip is farther from wrist than middle joint
    const wrist = landmarks[0];
    const tip = landmarks[indices[2]];
    const middle = landmarks[indices[1]];
    
    const tipDistance = this.calculateDistance(tip, wrist);
    const middleDistance = this.calculateDistance(middle, wrist);
    
    return tipDistance > middleDistance * 1.1;
  }

  /**
   * Calculate distance between two points
   * @param {Array} point1 - First point [x, y]
   * @param {Array} point2 - Second point [x, y]
   * @returns {number} Distance
   */
  calculateDistance(point1, point2) {
    return Math.sqrt(
      Math.pow(point1[0] - point2[0], 2) + 
      Math.pow(point1[1] - point2[1], 2)
    );
  }

  /**
   * Calculate hand size for normalization
   * @param {Array} landmarks - Hand landmarks
   * @returns {number} Hand size
   */
  calculateHandSize(landmarks) {
    const wrist = landmarks[0];
    const middleTip = landmarks[12];
    return this.calculateDistance(wrist, middleTip);
  }

  /**
   * Calculate finger spread for open palm detection
   * @param {Array} fingerTips - Array of finger tip positions
   * @returns {number} Spread score (0-1)
   */
  calculateFingerSpread(fingerTips) {
    let totalDistance = 0;
    let count = 0;
    
    for (let i = 0; i < fingerTips.length - 1; i++) {
      for (let j = i + 1; j < fingerTips.length; j++) {
        totalDistance += this.calculateDistance(fingerTips[i], fingerTips[j]);
        count++;
      }
    }
    
    const averageDistance = totalDistance / count;
    const handSize = this.calculateHandSize(fingerTips);
    
    return Math.min(1.0, averageDistance / (handSize * 2));
  }

  /**
   * Add recognition result to history
   * @param {Object} result - Recognition result
   */
  addToHistory(result) {
    this.gestureHistory.push({
      ...result,
      timestamp: Date.now()
    });
    
    if (this.gestureHistory.length > this.maxHistorySize) {
      this.gestureHistory.shift();
    }
  }

  /**
   * Get gesture history
   * @returns {Array} Gesture history
   */
  getHistory() {
    return [...this.gestureHistory];
  }

  /**
   * Get the most stable recent gesture
   * @param {number} timeWindow - Time window in milliseconds
   * @returns {Object|null} Most stable gesture
   */
  getStableGesture(timeWindow = 1000) {
    const now = Date.now();
    const recentGestures = this.gestureHistory.filter(
      g => now - g.timestamp <= timeWindow && g.gesture
    );
    
    if (recentGestures.length === 0) return null;
    
    // Count occurrences of each gesture
    const gestureCounts = {};
    recentGestures.forEach(g => {
      gestureCounts[g.gestureId] = (gestureCounts[g.gestureId] || 0) + 1;
    });
    
    // Find most frequent gesture
    const mostFrequent = Object.entries(gestureCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    if (mostFrequent && mostFrequent[1] >= 3) {
      return recentGestures.find(g => g.gestureId === mostFrequent[0]);
    }
    
    return null;
  }
}

// Export singleton instance
export const gestureRecognizer = new GestureRecognizer();
export default GestureRecognizer;
