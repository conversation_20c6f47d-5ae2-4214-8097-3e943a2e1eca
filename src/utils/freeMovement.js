/**
 * Enhanced 3D Movement System for Hand-Controlled 3D Game
 * 
 * This module provides advanced 3D movement capabilities including:
 * - Full 3D coordinate mapping (X, Y, Z axes)
 * - Hand depth detection using hand size analysis
 * - Smooth interpolation for all three dimensions
 * - Configurable sensitivity settings
 * - Boundary constraints for 3D scene
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

import { Vector3 } from "babylonjs";

/**
 * Configuration object for 3D movement system
 */
export const MovementConfig = {
  // Sensitivity settings for each axis (0.1 = slow, 1.0 = fast)
  sensitivity: {
    x: 0.8,    // Left/Right sensitivity
    y: 0.8,    // Up/Down sensitivity
    z: 0.6     // Forward/Backward sensitivity
  },
  
  // Interpolation factors for smooth movement (0.01 = very smooth, 0.5 = responsive)
  interpolation: {
    x: 0.12,   // X-axis smoothness
    y: 0.12,   // Y-axis smoothness
    z: 0.08    // Z-axis smoothness (slower for depth)
  },
  
  // Scene boundaries for 3D movement
  boundaries: {
    x: { min: -50, max: 50 },    // Left/Right bounds
    y: { min: -40, max: 40 },    // Up/Down bounds
    z: { min: -30, max: 30 }     // Forward/Backward bounds
  },
  
  // Webcam input dimensions
  input: {
    width: 640,   // Webcam width
    height: 480   // Webcam height
  },
  
  // Hand size analysis for depth detection
  depth: {
    baseHandSize: 120,        // Reference hand size at neutral depth
    minHandSize: 80,          // Minimum detectable hand size (far)
    maxHandSize: 200,         // Maximum detectable hand size (near)
    depthRange: 60,           // Total depth range in 3D units
    smoothingFactor: 0.15     // Depth change smoothing
  }
};

/**
 * Enhanced hand state processor for 3D movement
 * Analyzes hand landmarks to extract 3D position and depth information
 */
export class HandStateProcessor {
  constructor(config = MovementConfig) {
    this.config = config;
    this.lastDepth = 0;
    this.depthHistory = [];
    this.maxHistorySize = 5;
  }

  /**
   * Calculate hand size based on key landmarks
   * Uses the distance between wrist and middle finger tip as primary metric
   * @param {Array} landmarks - Hand landmark points from TensorFlow
   * @returns {number} Hand size in pixels
   */
  calculateHandSize(landmarks) {
    // Primary measurement: wrist to middle finger tip
    const wrist = landmarks[0];
    const middleFingerTip = landmarks[12];
    const primaryDistance = Math.sqrt(
      Math.pow(middleFingerTip[0] - wrist[0], 2) + 
      Math.pow(middleFingerTip[1] - wrist[1], 2)
    );

    // Secondary measurement: thumb to pinky span for validation
    const thumbTip = landmarks[4];
    const pinkyTip = landmarks[20];
    const spanDistance = Math.sqrt(
      Math.pow(pinkyTip[0] - thumbTip[0], 2) + 
      Math.pow(pinkyTip[1] - thumbTip[1], 2)
    );

    // Weighted average for more stable depth detection
    return (primaryDistance * 0.7) + (spanDistance * 0.3);
  }

  /**
   * Convert hand size to depth position
   * Larger hand = closer to camera = positive Z
   * Smaller hand = farther from camera = negative Z
   * @param {number} handSize - Calculated hand size in pixels
   * @returns {number} Depth position in 3D space
   */
  calculateDepth(handSize) {
    const { baseHandSize, minHandSize, maxHandSize, depthRange } = this.config.depth;
    
    // Clamp hand size to valid range
    const clampedSize = Math.max(minHandSize, Math.min(maxHandSize, handSize));
    
    // Normalize to 0-1 range (0 = far, 1 = near)
    const normalizedSize = (clampedSize - minHandSize) / (maxHandSize - minHandSize);
    
    // Convert to depth coordinate (-depthRange/2 to +depthRange/2)
    const rawDepth = (normalizedSize - 0.5) * depthRange;
    
    // Apply smoothing using history
    this.depthHistory.push(rawDepth);
    if (this.depthHistory.length > this.maxHistorySize) {
      this.depthHistory.shift();
    }
    
    // Calculate smoothed depth
    const averageDepth = this.depthHistory.reduce((sum, d) => sum + d, 0) / this.depthHistory.length;
    
    // Apply additional smoothing factor
    const smoothedDepth = this.lastDepth + (averageDepth - this.lastDepth) * this.config.depth.smoothingFactor;
    this.lastDepth = smoothedDepth;
    
    return smoothedDepth;
  }

  /**
   * Process hand landmarks into enhanced 3D hand state
   * @param {Array} landmarks - Hand landmark points from TensorFlow
   * @returns {Object} Enhanced hand state with 3D position and depth
   */
  processHandState(landmarks) {
    if (!landmarks || landmarks.length === 0) {
      return null;
    }

    // Calculate basic 2D position (using index finger base as reference)
    const indexBase = landmarks[5];
    const position2D = {
      x: indexBase[0],
      y: indexBase[1]
    };

    // Calculate hand size and depth
    const handSize = this.calculateHandSize(landmarks);
    const depth = this.calculateDepth(handSize);

    // Enhanced hand state with 3D information
    return {
      position2D,
      handSize,
      depth,
      landmarks,
      // Additional gesture data can be added here
      confidence: 1.0 // Could be enhanced with actual confidence metrics
    };
  }
}

/**
 * 3D Movement Controller
 * Handles the conversion from hand state to 3D object movement
 */
export class Movement3DController {
  constructor(config = MovementConfig) {
    this.config = config;
    this.lastPosition = new Vector3(0, 0, 0);
    this.targetPosition = new Vector3(0, 0, 0);
    this.handProcessor = new HandStateProcessor(config);
  }

  /**
   * Map 2D hand position to 3D scene coordinates
   * @param {Object} position2D - 2D hand position from camera
   * @returns {Object} 3D scene coordinates
   */
  mapTo3DCoordinates(position2D) {
    const { input, boundaries, sensitivity } = this.config;
    
    // Map X coordinate (left/right)
    const normalizedX = (position2D.x / input.width) - 0.5; // -0.5 to 0.5
    const sceneX = normalizedX * (boundaries.x.max - boundaries.x.min) * sensitivity.x;
    
    // Map Y coordinate (up/down) - flip Y axis for natural movement
    const normalizedY = 0.5 - (position2D.y / input.height); // -0.5 to 0.5 (flipped)
    const sceneY = normalizedY * (boundaries.y.max - boundaries.y.min) * sensitivity.y;
    
    return { x: sceneX, y: sceneY };
  }

  /**
   * Apply boundary constraints to 3D position
   * @param {Vector3} position - Target 3D position
   * @param {number} objectSize - Size of the object for boundary calculation
   * @returns {Vector3} Constrained position
   */
  applyBoundaryConstraints(position, objectSize = 5) {
    const { boundaries } = this.config;
    const offset = objectSize / 2;
    
    const constrainedPosition = new Vector3(
      Math.max(boundaries.x.min + offset, Math.min(boundaries.x.max - offset, position.x)),
      Math.max(boundaries.y.min + offset, Math.min(boundaries.y.max - offset, position.y)),
      Math.max(boundaries.z.min + offset, Math.min(boundaries.z.max - offset, position.z))
    );
    
    return constrainedPosition;
  }

  /**
   * Update 3D object position based on hand state
   * @param {Object} handState - Enhanced hand state from processor
   * @param {Object} object3D - Babylon.js 3D object to move
   * @returns {boolean} True if position was updated
   */
  updateObjectPosition(handState, object3D) {
    if (!handState || !object3D) {
      return false;
    }

    // Map 2D position to 3D coordinates
    const mapped3D = this.mapTo3DCoordinates(handState.position2D);
    
    // Set target position including depth
    this.targetPosition.x = mapped3D.x;
    this.targetPosition.y = mapped3D.y;
    this.targetPosition.z = handState.depth * this.config.sensitivity.z;
    
    // Apply boundary constraints
    const objectSize = object3D.scaling ? object3D.scaling.x * 5 : 5;
    const constrainedTarget = this.applyBoundaryConstraints(this.targetPosition, objectSize);
    
    // Apply smooth interpolation
    const { interpolation } = this.config;
    object3D.position.x += (constrainedTarget.x - object3D.position.x) * interpolation.x;
    object3D.position.y += (constrainedTarget.y - object3D.position.y) * interpolation.y;
    object3D.position.z += (constrainedTarget.z - object3D.position.z) * interpolation.z;
    
    return true;
  }

  /**
   * Get current movement configuration
   * @returns {Object} Current configuration
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * Update movement configuration
   * @param {Object} newConfig - New configuration values
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.handProcessor.config = this.config;
  }
}

/**
 * Factory function to create a new 3D movement system
 * @param {Object} customConfig - Custom configuration overrides
 * @returns {Movement3DController} Configured 3D movement controller
 */
export function createMovement3D(customConfig = {}) {
  const config = { ...MovementConfig, ...customConfig };
  return new Movement3DController(config);
}

export default Movement3DController;
