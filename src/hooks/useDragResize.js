import { useState, useCallback, useEffect } from 'react';

/**
 * Custom hook for drag and resize functionality
 */
export const useDragResize = (initialPosition = { x: 20, y: 20 }, initialSize = { width: 400, height: 500 }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);

  const handleMouseDown = useCallback((e, action) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (action === 'drag') {
      setIsDragging(true);
      // Calculate offset from mouse to panel's top-left corner
      const panelLeft = window.innerWidth - position.x - size.width;
      const panelTop = window.innerHeight - position.y - size.height;
      setDragStart({
        x: e.clientX - panelLeft,
        y: e.clientY - panelTop
      });
    } else if (action === 'resize') {
      setIsResizing(true);
      setDragStart({
        x: e.clientX,
        y: e.clientY
      });
    }
  }, [position, size]);

  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      // Calculate new position based on mouse movement
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;
      
      // Apply viewport boundaries (using right/bottom positioning)
      const boundedX = Math.max(0, Math.min(window.innerWidth - size.width, newX));
      const boundedY = Math.max(0, Math.min(window.innerHeight - size.height, newY));
      
      // Convert to right/bottom positioning for CSS
      setPosition({ 
        x: window.innerWidth - boundedX - size.width, 
        y: window.innerHeight - boundedY - size.height 
      });
    } else if (isResizing) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      const newWidth = Math.max(300, Math.min(window.innerWidth - (window.innerWidth - position.x - size.width), size.width + deltaX));
      const newHeight = Math.max(200, Math.min(window.innerHeight - (window.innerHeight - position.y - size.height), size.height + deltaY));
      
      setSize({ width: newWidth, height: newHeight });
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, [isDragging, isResizing, dragStart, position, size]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  // Add global mouse event listeners
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  return {
    isDragging,
    isResizing,
    position,
    size,
    handleMouseDown,
    setPosition,
    setSize
  };
};
