import React, { useRef, useEffect, useState, useCallback } from 'react'
import * as tf from '@tensorflow/tfjs';
import * as handpose from '@tensorflow-models/handpose';
import Webcam from 'react-webcam';
import { drawHand, handPathTracker } from './utils';
import Game from './utils/game';

import './App.css'

function App() {
  const webcamRef = useRef();
  const canvasRef = useRef();
  const gameCanvasRef = useRef();
  const gameRef = useRef();
  const [isLoading, setIsLoading] = useState(false);
  const [movementMode, setMovementMode] = useState('2D');
  const [showDepthInfo, setShowDepthInfo] = useState(false);
  const [circularMode, setCircularMode] = useState(false);
  const [objectMovementMode, setObjectMovementMode] = useState('smooth');
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);
  const [showAdaptiveInstructions, setShowAdaptiveInstructions] = useState(true);
  const [handTrackingStatus, setHandTrackingStatus] = useState('Initializing...');
  const [instructionsMinimized, setInstructionsMinimized] = useState(false);

  const initializeHandpose = async () => {

    setIsLoading(true);


    const net = await handpose.load();
    setIsLoading(false);
    setInterval(() => {
      detect(net);
    }, 50)
  }
  const detect = async (net) => {
    if (webcamRef.current && webcamRef.current.video.readyState === 4) {
      canvasRef.current.width = webcamRef.current.video.videoWidth;
      canvasRef.current.height = webcamRef.current.video.videoHeight;

      const hand = await net.estimateHands(webcamRef.current.video);
      // Pass the movement mode to enable 3D detection when needed
      const enable3D = movementMode === '3D';

      // Get circular motion controller if circular mode is enabled
      const circularController = (circularMode || movementMode === 'circular')
        ? gameRef.current?.getCircularMotionController()
        : null;

      const handState = drawHand(hand, canvasRef.current.getContext('2d'), enable3D, circularController);

      // Update hand tracking status
      if (handState && handState.detectionStatus) {
        setHandTrackingStatus(handState.detectionStatus);
      }

      gameRef.current.moveBox(handState);
    }
  }

  const toggleMovementMode = () => {
    const modes = ['2D', '3D', 'circular'];
    const currentIndex = modes.indexOf(movementMode);
    const newMode = modes[(currentIndex + 1) % modes.length];

    setMovementMode(newMode);
    if (gameRef.current) {
      gameRef.current.setMovementMode(newMode);
    }
    setShowDepthInfo(newMode === '3D');
    setCircularMode(newMode === 'circular');
  }

  const toggleCircularMode = () => {
    const newCircularMode = !circularMode;
    setCircularMode(newCircularMode);
    if (gameRef.current) {
      gameRef.current.toggleCircularMode();
    }
  }

  const toggleObjectMovementMode = () => {
    const modes = ['direct', 'smooth', 'physics', 'responsive'];
    const currentIndex = modes.indexOf(objectMovementMode);
    const newMode = modes[(currentIndex + 1) % modes.length];

    setObjectMovementMode(newMode);
    if (gameRef.current) {
      gameRef.current.setObjectMovementMode(newMode);
    }
  }

  const togglePerformanceMetrics = () => {
    const newShow = !showPerformanceMetrics;
    setShowPerformanceMetrics(newShow);
    if (gameRef.current) {
      gameRef.current.togglePerformanceMetrics();
    }
  }

  const togglePathTrail = useCallback(() => {
    handPathTracker.toggle();
  }, []);

  const toggleInstructions = () => {
    setInstructionsMinimized(!instructionsMinimized);
  }

  // Keyboard event handler
  const handleKeyPress = useCallback((event) => {
    switch(event.key.toLowerCase()) {
      case 't':
        togglePathTrail();
        break;
      case 'p':
        togglePerformanceMetrics();
        break;
      case 'm':
        toggleObjectMovementMode();
        break;
      case 'i':
        setShowAdaptiveInstructions(prev => !prev);
        if (gameRef.current) {
          gameRef.current.toggleAdaptiveInstructions();
        }
        break;
    }
  }, [togglePathTrail]);

  // Add keyboard event listeners
  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  useEffect(() => {
    gameCanvasRef.current.width = window.innerWidth / 2;
    gameCanvasRef.current.height = window.innerHeight - 100;

    // Initialize game with movement mode options
    gameRef.current = new Game(gameCanvasRef.current, {
      movementMode: movementMode,
      movement3DConfig: {
        // Custom 3D movement configuration can be added here
        sensitivity: { x: 0.8, y: 0.8, z: 0.6 },
        interpolation: { x: 0.12, y: 0.12, z: 0.08 }
      },
      circularMotionConfig: {
        // Custom circular motion configuration
        detection: {
          minPoints: 8,
          radiusThreshold: 30,
          angleThreshold: Math.PI * 1.5
        },
        motion: {
          followSpeed: 0.2,
          radiusScale: 0.5,
          heightVariation: 10
        }
      }
    });
    initializeHandpose();
  }, [])
  return (
    <>
      {/* Navigation Bar */}
      <nav className="navbar">
        <div className="navbar-brand">
          <h1>🎮 3D Hand Tracking Game</h1>
          <div className="status-display">
            <span className={`status-indicator ${handTrackingStatus.includes('High quality') ? 'good' :
                                                handTrackingStatus.includes('detected') ? 'ok' : 'poor'}`}>
              ●
            </span>
            <span className="status-text">{handTrackingStatus}</span>
          </div>
        </div>

        <div className="navbar-controls">
          {/* Movement Mode Controls */}
          <div className="control-group">
            <label>Movement:</label>
            <div className="button-group">
              <button
                onClick={toggleMovementMode}
                className={`nav-button ${movementMode === '2D' ? 'active' : ''}`}
                title="2D Movement Mode"
              >
                2D
              </button>
              <button
                onClick={toggleMovementMode}
                className={`nav-button ${movementMode === '3D' ? 'active' : ''}`}
                title="3D Movement Mode"
              >
                3D
              </button>
              <button
                onClick={toggleMovementMode}
                className={`nav-button ${movementMode === 'circular' ? 'active' : ''}`}
                title="Circular Movement Mode"
              >
                🔄
              </button>
            </div>
          </div>

          {/* Physics Mode Controls */}
          <div className="control-group">
            <label>Physics:</label>
            <button
              onClick={toggleObjectMovementMode}
              className="nav-button physics-mode"
              title={`Current: ${objectMovementMode} (Click to cycle)`}
            >
              ⚡ {objectMovementMode}
            </button>
          </div>

          {/* Feature Toggles */}
          <div className="control-group">
            <label>Features:</label>
            <div className="button-group">
              <button
                onClick={togglePathTrail}
                className="nav-button"
                title="Toggle hand path trail (T)"
              >
                ✨
              </button>
              <button
                onClick={togglePerformanceMetrics}
                className={`nav-button ${showPerformanceMetrics ? 'active' : ''}`}
                title="Toggle performance metrics (P)"
              >
                📊
              </button>
              <button
                onClick={toggleCircularMode}
                className={`nav-button ${circularMode ? 'active' : ''}`}
                title="Toggle circular motion"
              >
                🔄
              </button>
              <button
                onClick={toggleInstructions}
                className={`nav-button ${!instructionsMinimized ? 'active' : ''}`}
                title="Toggle instructions panel"
              >
                💡
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="App">
        <div className="webcam-ai__container">
          {isLoading && <h1 style={{zIndex: 10, color: '#fff'}}>Initializing AI Model...</h1>}
          <Webcam ref={webcamRef} className="webcam"/>
          <canvas className="webcam-ai__canvas" ref={canvasRef}/>
        </div>
        <div className="game">
          <canvas className="game__canvas" ref={gameCanvasRef}/>
        </div>
      </div>
      {/* Minimizable Instructions Panel */}
      {!instructionsMinimized && (
        <footer className="footer">
          <div className="instructions">
            <div className="instructions-header">
              <h2>📖 Instructions & Controls</h2>
              <button
                onClick={toggleInstructions}
                className="minimize-button"
                title="Minimize instructions"
              >
                ➖
              </button>
            </div>
            {/* Hand Controls */}
            <div className="control-section">
              <h3>🖐️ Hand Controls</h3>
              <ul>
                <li>👆 Move Hand: Control box position {movementMode === '3D' ? '(X, Y, Z)' : movementMode === 'circular' ? '(Circular Path)' : '(X, Y)'}</li>
                <li>🤏 Pinch: Enter resize mode</li>
                <li>✋ While Pinched: Move hand apart/together to resize</li>
                <li>👋 Release Pinch: Return to movement mode</li>
                {movementMode === '3D' && (
                  <>
                    <li>🔄 Move Closer/Farther: Control depth (Z-axis)</li>
                    <li>📏 Hand Size: Determines forward/backward position</li>
                  </>
                )}
                {(movementMode === 'circular' || circularMode) && (
                  <>
                    <li>🔄 Draw Circles: Move finger in circular motion to make object follow</li>
                    <li>↻ Direction: Clockwise or counterclockwise detection</li>
                    <li>📐 Radius: Larger circles = wider object movement</li>
                  </>
                )}
              </ul>
            </div>

            {/* Gesture Controls */}
            <div className="control-section">
              <h3>👋 Gesture Controls</h3>
              <ul>
                <li>Peace Sign: Toggle movement mode</li>
                <li>Thumbs Up: Increase speed</li>
                <li>Thumbs Down: Decrease speed</li>
                <li>Open Palm: Reset position</li>
                <li>Fist: Pause movement</li>
              </ul>
            </div>

            {/* Keyboard Shortcuts */}
            <div className="control-section">
              <h3>⌨️ Keyboard Shortcuts</h3>
              <div className="shortcuts-grid">
                <span>T</span><span>Toggle hand trail</span>
                <span>P</span><span>Performance metrics</span>
                <span>M</span><span>Physics mode</span>
                <span>I</span><span>Toggle instructions</span>
              </div>
            </div>

            {/* Physics Modes */}
            <div className="control-section">
              <h3>⚡ Physics Modes</h3>
              <ul>
                <li><strong>Direct:</strong> Immediate response, no lag</li>
                <li><strong>Smooth:</strong> Balanced smoothness (default)</li>
                <li><strong>Physics:</strong> Full physics with momentum</li>
                <li><strong>Responsive:</strong> Quick response with light physics</li>
              </ul>
            </div>

            {/* Dynamic Info */}
            {showDepthInfo && (
              <div className="depth-info">
                <p><strong>3D Mode Active:</strong> Move your hand closer or farther from the camera to control depth!</p>
              </div>
            )}
            {(circularMode || movementMode === 'circular') && (
              <div className="circular-info">
                <p><strong>🔄 Circular Motion Mode:</strong> Draw circles with your finger to make the object follow circular paths!</p>
              </div>
            )}
          </div>
        </footer>
      )}
    </>
  )
}

export default App
