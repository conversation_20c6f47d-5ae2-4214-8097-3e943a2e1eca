import React, { useRef, useEffect, useState, useCallback } from 'react'
import * as tf from '@tensorflow/tfjs';
import * as handpose from '@tensorflow-models/handpose';
import Webcam from 'react-webcam';
import { drawHand, handPathTracker } from './utils';
import Game from './utils/game';

import './App.css'

function App() {
  const webcamRef = useRef();
  const canvasRef = useRef();
  const gameCanvasRef = useRef();
  const gameRef = useRef();
  const [isLoading, setIsLoading] = useState(false);
  const [movementMode, setMovementMode] = useState('2D');
  const [showDepthInfo, setShowDepthInfo] = useState(false);
  const [circularMode, setCircularMode] = useState(false);
  const [objectMovementMode, setObjectMovementMode] = useState('smooth');
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);
  const [showAdaptiveInstructions, setShowAdaptiveInstructions] = useState(true);
  const [handTrackingStatus, setHandTrackingStatus] = useState('Initializing...');

  const initializeHandpose = async () => {

    setIsLoading(true);


    const net = await handpose.load();
    setIsLoading(false);
    setInterval(() => {
      detect(net);
    }, 50)
  }
  const detect = async (net) => {
    if (webcamRef.current && webcamRef.current.video.readyState === 4) {
      canvasRef.current.width = webcamRef.current.video.videoWidth;
      canvasRef.current.height = webcamRef.current.video.videoHeight;

      const hand = await net.estimateHands(webcamRef.current.video);
      // Pass the movement mode to enable 3D detection when needed
      const enable3D = movementMode === '3D';

      // Get circular motion controller if circular mode is enabled
      const circularController = (circularMode || movementMode === 'circular')
        ? gameRef.current?.getCircularMotionController()
        : null;

      const handState = drawHand(hand, canvasRef.current.getContext('2d'), enable3D, circularController);

      // Update hand tracking status
      if (handState && handState.detectionStatus) {
        setHandTrackingStatus(handState.detectionStatus);
      }

      gameRef.current.moveBox(handState);
    }
  }

  const toggleMovementMode = () => {
    const modes = ['2D', '3D', 'circular'];
    const currentIndex = modes.indexOf(movementMode);
    const newMode = modes[(currentIndex + 1) % modes.length];

    setMovementMode(newMode);
    if (gameRef.current) {
      gameRef.current.setMovementMode(newMode);
    }
    setShowDepthInfo(newMode === '3D');
    setCircularMode(newMode === 'circular');
  }

  const toggleCircularMode = () => {
    const newCircularMode = !circularMode;
    setCircularMode(newCircularMode);
    if (gameRef.current) {
      gameRef.current.toggleCircularMode();
    }
  }

  const toggleObjectMovementMode = () => {
    const modes = ['direct', 'smooth', 'physics', 'responsive'];
    const currentIndex = modes.indexOf(objectMovementMode);
    const newMode = modes[(currentIndex + 1) % modes.length];

    setObjectMovementMode(newMode);
    if (gameRef.current) {
      gameRef.current.setObjectMovementMode(newMode);
    }
  }

  const togglePerformanceMetrics = () => {
    const newShow = !showPerformanceMetrics;
    setShowPerformanceMetrics(newShow);
    if (gameRef.current) {
      gameRef.current.togglePerformanceMetrics();
    }
  }

  const togglePathTrail = useCallback(() => {
    handPathTracker.toggle();
  }, []);

  // Keyboard event handler
  const handleKeyPress = useCallback((event) => {
    switch(event.key.toLowerCase()) {
      case 't':
        togglePathTrail();
        break;
      case 'p':
        togglePerformanceMetrics();
        break;
      case 'm':
        toggleObjectMovementMode();
        break;
      case 'i':
        setShowAdaptiveInstructions(prev => !prev);
        if (gameRef.current) {
          gameRef.current.toggleAdaptiveInstructions();
        }
        break;
    }
  }, [togglePathTrail]);

  // Add keyboard event listeners
  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);
  useEffect(() => {
    gameCanvasRef.current.width = window.innerWidth / 2;
    gameCanvasRef.current.height = window.innerHeight - 100;

    // Initialize game with movement mode options
    gameRef.current = new Game(gameCanvasRef.current, {
      movementMode: movementMode,
      movement3DConfig: {
        // Custom 3D movement configuration can be added here
        sensitivity: { x: 0.8, y: 0.8, z: 0.6 },
        interpolation: { x: 0.12, y: 0.12, z: 0.08 }
      },
      circularMotionConfig: {
        // Custom circular motion configuration
        detection: {
          minPoints: 8,
          radiusThreshold: 30,
          angleThreshold: Math.PI * 1.5
        },
        motion: {
          followSpeed: 0.2,
          radiusScale: 0.5,
          heightVariation: 10
        }
      }
    });
    initializeHandpose();
  }, [])
  return (
    <>
      <div className="App">
        <div className="webcam-ai__container">
          {isLoading && <h1 style={{zIndex: 10, color: '#fff'}}>Initializing AI Model...</h1>}
          <Webcam ref={webcamRef} className="webcam"/>
          <canvas className="webcam-ai__canvas" ref={canvasRef}/>
        </div>
        <div className="game">
          <canvas className="game__canvas" ref={gameCanvasRef}/>
        </div>
      </div>
      <footer className="footer">
        <div className="instructions">
          <h2>Enhanced Hand Controls</h2>

          {/* Hand Tracking Status */}
          <div className="status-display">
            <span className={`status-indicator ${handTrackingStatus.includes('High quality') ? 'good' :
                                                handTrackingStatus.includes('detected') ? 'ok' : 'poor'}`}>
              ●
            </span>
            <span className="status-text">{handTrackingStatus}</span>
          </div>

          <div className="movement-mode-toggle">
            <button
              onClick={toggleMovementMode}
              className={`mode-button ${movementMode === '2D' ? 'active' : ''}`}
            >
              2D Mode
            </button>
            <button
              onClick={toggleMovementMode}
              className={`mode-button ${movementMode === '3D' ? 'active' : ''}`}
            >
              3D Mode
            </button>
            <button
              onClick={toggleMovementMode}
              className={`mode-button ${movementMode === 'circular' ? 'active' : ''}`}
            >
              🔄 Circular
            </button>
          </div>

          {/* Object Movement Mode Toggle */}
          <div className="object-movement-toggle">
            <button
              onClick={toggleObjectMovementMode}
              className="physics-button"
              title="Cycle through movement physics modes"
            >
              ⚡ Physics: {objectMovementMode}
            </button>
          </div>

          <div className="circular-toggle">
            <button
              onClick={toggleCircularMode}
              className={`circular-button ${circularMode ? 'active' : ''}`}
            >
              {circularMode ? '🔄 Circular Motion ON' : '⭕ Enable Circular Motion'}
            </button>
          </div>

          {/* Enhanced Controls */}
          <div className="enhanced-controls">
            <button
              onClick={togglePerformanceMetrics}
              className={`control-button ${showPerformanceMetrics ? 'active' : ''}`}
              title="Toggle performance metrics (P)"
            >
              📊 Metrics
            </button>
            <button
              onClick={togglePathTrail}
              className="control-button"
              title="Toggle hand path trail (T)"
            >
              ✨ Trail
            </button>
            <button
              onClick={() => {
                setShowAdaptiveInstructions(!showAdaptiveInstructions);
                if (gameRef.current) {
                  gameRef.current.toggleAdaptiveInstructions();
                }
              }}
              className={`control-button ${showAdaptiveInstructions ? 'active' : ''}`}
              title="Toggle adaptive instructions (I)"
            >
              💡 Help
            </button>
          </div>
          <ul>
            <li>👆 Move Hand: Control box position {movementMode === '3D' ? '(X, Y, Z)' : movementMode === 'circular' ? '(Circular Path)' : '(X, Y)'}</li>
            <li>🤏 Pinch: Enter resize mode</li>
            <li>✋ While Pinched: Move hand apart/together to resize</li>
            <li>👋 Release Pinch: Return to movement mode</li>
            {movementMode === '3D' && (
              <>
                <li>🔄 Move Closer/Farther: Control depth (Z-axis)</li>
                <li>📏 Hand Size: Determines forward/backward position</li>
              </>
            )}
            {(movementMode === 'circular' || circularMode) && (
              <>
                <li>🔄 Draw Circles: Move finger in circular motion to make object follow</li>
                <li>↻ Direction: Clockwise or counterclockwise detection</li>
                <li>📐 Radius: Larger circles = wider object movement</li>
              </>
            )}
          </ul>

          {/* Keyboard Shortcuts */}
          <div className="keyboard-shortcuts">
            <h3>Keyboard Shortcuts</h3>
            <div className="shortcuts-grid">
              <span>T</span><span>Toggle hand trail</span>
              <span>P</span><span>Performance metrics</span>
              <span>M</span><span>Physics mode</span>
              <span>I</span><span>Instructions</span>
            </div>
          </div>
          {showDepthInfo && (
            <div className="depth-info">
              <p><strong>3D Mode Active:</strong> Move your hand closer or farther from the camera to control depth!</p>
            </div>
          )}
          {(circularMode || movementMode === 'circular') && (
            <div className="circular-info">
              <p><strong>🔄 Circular Motion Mode:</strong> Draw circles with your finger to make the object follow circular paths!</p>
            </div>
          )}
        </div>
      </footer>
    </>
  )
}

export default App
