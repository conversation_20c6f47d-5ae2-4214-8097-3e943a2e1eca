body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  overflow: hidden; /* Prevent scrolling */
}

html {
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navbar Styles - Fixed height for viewport constraint */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  height: 70px; /* Fixed height */
  flex-shrink: 0; /* Prevent shrinking */
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-brand h1 {
  margin: 0;
  font-size: 1.5em;
  font-weight: bold;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 0.9em;
  font-weight: 500;
  color: #E0E0E0;
  min-width: 60px;
}

.button-group {
  display: flex;
  gap: 4px;
}

/* Enhanced Navbar Buttons with Tooltips */
.nav-button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85em;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.nav-button.active {
  background: rgba(76, 175, 80, 0.8);
  border-color: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.nav-button.physics-mode {
  background: rgba(156, 39, 176, 0.8);
  border-color: #9C27B0;
  text-transform: capitalize;
}

.nav-button.physics-mode:hover {
  background: rgba(156, 39, 176, 1);
}

/* Main App Container - Viewport constrained layout */
.App {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 70px); /* Account for navbar height */
  overflow: hidden;
}

/* Content Area - Webcam and Game side by side */
.content-area {
  display: flex;
  flex: 1;
  gap: 15px;
  padding: 15px;
  overflow: hidden;
  min-height: 0; /* Allow flexbox to shrink */
}

/* Webcam Container - Fixed positioning for proper canvas alignment */
.webcam-ai__container {
  width: 50%;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Webcam Video - Base layer */
.webcam {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: block;
}

/* Hand Detection Canvas - Overlay layer */
.webcam-ai__canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  border-radius: 8px;
  z-index: 2;
  pointer-events: none;
  object-fit: cover;
}

/* Game Container */
.game {
  width: 50%;
  position: relative;
  height: 100%;
  overflow: hidden;
}

.game__canvas {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: block;
}

/* Enhanced Floating Instructions Panel - Draggable and Resizable */
.instructions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 12px;
  z-index: 1000;
  width: 400px;
  height: 500px;
  min-width: 300px;
  min-height: 200px;
  max-width: 90vw;
  max-height: 80vh;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  transition: box-shadow 0.3s ease;
  resize: both;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.instructions:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.7);
  border-color: rgba(255, 255, 255, 0.3);
}

.instructions.dragging {
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.8);
  border-color: rgba(76, 175, 80, 0.5);
}

.instructions.minimized {
  height: auto;
  max-height: none;
  overflow: visible;
}

/* Draggable Header */
.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px 10px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  cursor: move;
  user-select: none;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px 10px 0 0;
  flex-shrink: 0;
}

.instructions-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.instructions-header:active {
  background: rgba(76, 175, 80, 0.2);
}

/* Content area with scroll */
.instructions-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.instructions-content::-webkit-scrollbar {
  width: 6px;
}

.instructions-content::-webkit-scrollbar-track {
  background: transparent;
}

.instructions-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.instructions-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.instructions-header h2 {
  margin: 0;
  font-size: 1.3em;
  color: #4CAF50;
}

/* Minimize Button */
.minimize-button {
  background: rgba(255, 107, 107, 0.8);
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.3s ease;
  user-select: none;
}

.minimize-button:hover {
  background: rgba(255, 107, 107, 1);
  transform: scale(1.05);
}

/* Resize Handles */
.resize-handle {
  position: absolute;
  background: rgba(76, 175, 80, 0.6);
  transition: background 0.2s ease;
}

.resize-handle:hover {
  background: rgba(76, 175, 80, 0.8);
}

/* Corner resize handles */
.resize-handle.corner {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.resize-handle.corner.nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle.corner.ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle.corner.sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-handle.corner.se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* Edge resize handles */
.resize-handle.edge.n {
  top: -3px;
  left: 12px;
  right: 12px;
  height: 6px;
  cursor: n-resize;
}

.resize-handle.edge.s {
  bottom: -3px;
  left: 12px;
  right: 12px;
  height: 6px;
  cursor: s-resize;
}

.resize-handle.edge.w {
  left: -3px;
  top: 12px;
  bottom: 12px;
  width: 6px;
  cursor: w-resize;
}

.resize-handle.edge.e {
  right: -3px;
  top: 12px;
  bottom: 12px;
  width: 6px;
  cursor: e-resize;
}

.control-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4CAF50;
}

.control-section h3 {
  margin: 0 0 10px 0;
  font-size: 1.1em;
  color: #FFD700;
}

.control-section ul {
  margin: 0;
  padding-left: 20px;
}

.control-section li {
  margin: 6px 0;
  font-size: 0.9em;
  line-height: 1.4;
}

/* Tooltip Styles */
.nav-button[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8em;
  white-space: nowrap;
  z-index: 1001;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  animation: tooltip-fade-in 0.2s ease;
}

.nav-button[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1001;
}

@keyframes tooltip-fade-in {
  from { opacity: 0; transform: translateX(-50%) translateY(5px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Status Display Styles */
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.85em;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  font-size: 10px;
  animation: pulse-status 2s infinite;
}

.status-indicator.good {
  color: #00FF00;
}

.status-indicator.ok {
  color: #FFFF00;
}

.status-indicator.poor {
  color: #FF6B6B;
}

.status-text {
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.8em;
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 5px 15px;
  font-size: 0.85em;
  align-items: center;
}

.shortcuts-grid span:nth-child(odd) {
  background: rgba(0, 188, 212, 0.2);
  color: #00BCD4;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
  min-width: 20px;
}

.shortcuts-grid span:nth-child(even) {
  color: #FFFFFF;
}

/* Legacy styles for backward compatibility */
.depth-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 8px;
  font-size: 0.85em;
}

.depth-info p {
  margin: 0;
  color: #4CAF50;
  font-weight: bold;
}

.circular-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #FF6B6B;
  border-radius: 8px;
  font-size: 0.85em;
}

.circular-info p {
  margin: 0;
  color: #FF6B6B;
  font-weight: bold;
}

/* Responsive Design - Viewport constrained */
@media (max-width: 1024px) {
  .content-area {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .webcam-ai__container,
  .game {
    width: 100%;
    height: 50%;
  }

  .instructions {
    width: 350px;
    height: 400px;
    max-height: 60vh;
    right: 10px;
    bottom: 10px;
  }
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 8px;
    padding: 10px;
    height: auto;
    min-height: 80px;
  }

  .App {
    height: calc(100vh - 80px);
  }

  .navbar-controls {
    gap: 8px;
    justify-content: center;
  }

  .control-group {
    flex-direction: column;
    gap: 4px;
    align-items: center;
  }

  .control-group label {
    min-width: auto;
    font-size: 0.8em;
  }

  .nav-button {
    padding: 4px 8px;
    font-size: 0.75em;
  }

  .content-area {
    gap: 8px;
    padding: 8px;
  }

  .instructions {
    width: 300px;
    height: 350px;
    max-width: 95vw;
    max-height: 50vh;
    right: 5px;
    bottom: 5px;
  }

  .instructions-content {
    padding: 15px;
  }

  .shortcuts-grid {
    gap: 3px 10px;
    font-size: 0.8em;
  }
}

@media (max-width: 480px) {
  .navbar-brand h1 {
    font-size: 1.2em;
  }

  .navbar-controls {
    flex-direction: column;
    gap: 8px;
  }

  .button-group {
    justify-content: center;
  }

  .instructions {
    width: 280px;
    font-size: 0.9em;
  }

  .control-section {
    padding: 10px;
  }

  .control-section h3 {
    font-size: 1em;
  }
}
