body {
  margin: 0;
  padding: 0;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 1000;
  min-height: 60px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-brand h1 {
  margin: 0;
  font-size: 1.5em;
  font-weight: bold;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 0.9em;
  font-weight: 500;
  color: #E0E0E0;
  min-width: 60px;
}

.button-group {
  display: flex;
  gap: 4px;
}

/* Enhanced Navbar Buttons with Tooltips */
.nav-button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85em;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.nav-button.active {
  background: rgba(76, 175, 80, 0.8);
  border-color: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.nav-button.physics-mode {
  background: rgba(156, 39, 176, 0.8);
  border-color: #9C27B0;
  text-transform: capitalize;
}

.nav-button.physics-mode:hover {
  background: rgba(156, 39, 176, 1);
}

/* Main App Container - Restructured Layout */
.App {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 80px);
  padding: 20px;
  gap: 20px;
}

/* Content Area - Webcam and Game side by side */
.content-area {
  display: flex;
  flex: 1;
  gap: 20px;
  min-height: 600px;
}

.webcam-ai__container {
  width: 50%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.game {
  width: 50%;
  position: relative;
}

.webcam-ai__container > .webcam,
.webcam-ai__container > .webcam-ai__canvas {
  position: relative;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.webcam-ai__canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  pointer-events: none;
}

.game__canvas {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Floating Instructions Panel */
.instructions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px 30px;
  border-radius: 12px;
  z-index: 1000;
  width: 400px;
  max-width: 90vw;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  max-height: 70vh;
  overflow-y: auto;
  transition: all 0.3s ease;
  cursor: move; /* Indicates it can be dragged */
}

.instructions.minimized {
  height: auto;
  max-height: none;
  overflow: visible;
}

.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
  cursor: move;
}

.instructions-header h2 {
  margin: 0;
  font-size: 1.3em;
  color: #4CAF50;
}

.minimize-button {
  background: rgba(255, 107, 107, 0.8);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.3s ease;
}

.minimize-button:hover {
  background: rgba(255, 107, 107, 1);
  transform: scale(1.1);
}

.control-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4CAF50;
}

.control-section h3 {
  margin: 0 0 10px 0;
  font-size: 1.1em;
  color: #FFD700;
}

.control-section ul {
  margin: 0;
  padding-left: 20px;
}

.control-section li {
  margin: 6px 0;
  font-size: 0.9em;
  line-height: 1.4;
}

/* Tooltip Styles */
.nav-button[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8em;
  white-space: nowrap;
  z-index: 1001;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  animation: tooltip-fade-in 0.2s ease;
}

.nav-button[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1001;
}

@keyframes tooltip-fade-in {
  from { opacity: 0; transform: translateX(-50%) translateY(5px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Status Display Styles */
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.85em;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  font-size: 10px;
  animation: pulse-status 2s infinite;
}

.status-indicator.good {
  color: #00FF00;
}

.status-indicator.ok {
  color: #FFFF00;
}

.status-indicator.poor {
  color: #FF6B6B;
}

.status-text {
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.8em;
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 5px 15px;
  font-size: 0.85em;
  align-items: center;
}

.shortcuts-grid span:nth-child(odd) {
  background: rgba(0, 188, 212, 0.2);
  color: #00BCD4;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
  min-width: 20px;
}

.shortcuts-grid span:nth-child(even) {
  color: #FFFFFF;
}

/* Legacy styles for backward compatibility */
.depth-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 8px;
  font-size: 0.85em;
}

.depth-info p {
  margin: 0;
  color: #4CAF50;
  font-weight: bold;
}

.circular-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #FF6B6B;
  border-radius: 8px;
  font-size: 0.85em;
}

.circular-info p {
  margin: 0;
  color: #FF6B6B;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-area {
    flex-direction: column;
    gap: 15px;
  }

  .webcam-ai__container,
  .game {
    width: 100%;
  }

  .instructions {
    width: 350px;
    right: 10px;
    bottom: 10px;
  }
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .navbar-controls {
    gap: 10px;
    justify-content: center;
  }

  .control-group {
    flex-direction: column;
    gap: 5px;
    align-items: center;
  }

  .control-group label {
    min-width: auto;
    font-size: 0.8em;
  }

  .nav-button {
    padding: 5px 8px;
    font-size: 0.75em;
  }

  .App {
    padding: 10px;
    gap: 15px;
  }

  .content-area {
    min-height: 400px;
  }

  .instructions {
    width: 300px;
    max-width: 95vw;
    padding: 15px 20px;
    right: 5px;
    bottom: 5px;
  }

  .shortcuts-grid {
    gap: 3px 10px;
    font-size: 0.8em;
  }
}

@media (max-width: 480px) {
  .navbar-brand h1 {
    font-size: 1.2em;
  }

  .navbar-controls {
    flex-direction: column;
    gap: 8px;
  }

  .button-group {
    justify-content: center;
  }

  .instructions {
    width: 280px;
    font-size: 0.9em;
  }

  .control-section {
    padding: 10px;
  }

  .control-section h3 {
    font-size: 1em;
  }
}
