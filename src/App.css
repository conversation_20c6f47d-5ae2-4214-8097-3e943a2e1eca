body {
  margin: 0;
  padding: 0;
}

.App {
  display: flex;
  position: relative;
}

.App > * {
  position: relative;
}

.webcam-ai__container {
  width: 50%;
  position: relative;
}

.game {
  width: 50%;
}

.webcam-ai__container > * {
  position: absolute;
  width: 100%;
  z-index: 1;
}

.webcam-ai__canvas {
  z-index: 2;
}

.instructions {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  z-index: 2;
  width: 80%;
  max-width: 400px;
  text-align: center;
  backdrop-filter: blur(5px);
}

.instructions h2 {
  margin: 0 0 10px 0;
  font-size: 1.2em;
  color: #4CAF50;
}

.instructions ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.instructions li {
  margin: 8px 0;
  font-size: 0.95em;
}

.instructions li:before {
  content: "•";
  color: #4CAF50;
  font-weight: bold;
  margin-right: 8px;
}

.movement-mode-toggle {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  justify-content: center;
}

.mode-button {
  padding: 8px 16px;
  border: 2px solid #4CAF50;
  background: transparent;
  color: #4CAF50;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  transition: all 0.3s ease;
}

.mode-button:hover {
  background: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

.mode-button.active {
  background: #4CAF50;
  color: white;
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.depth-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 8px;
  font-size: 0.85em;
}

.depth-info p {
  margin: 0;
  color: #4CAF50;
  font-weight: bold;
}

.circular-toggle {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.circular-button {
  padding: 8px 16px;
  border: 2px solid #FF6B6B;
  background: transparent;
  color: #FF6B6B;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  transition: all 0.3s ease;
}

.circular-button:hover {
  background: rgba(255, 107, 107, 0.1);
  transform: translateY(-2px);
}

.circular-button.active {
  background: #FF6B6B;
  color: white;
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3); }
  50% { box-shadow: 0 6px 12px rgba(255, 107, 107, 0.5); }
  100% { box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3); }
}

.circular-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #FF6B6B;
  border-radius: 8px;
  font-size: 0.85em;
}

.circular-info p {
  margin: 0;
  color: #FF6B6B;
  font-weight: bold;
}
