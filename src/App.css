body {
  margin: 0;
  padding: 0;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 1000;
  min-height: 60px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-brand h1 {
  margin: 0;
  font-size: 1.5em;
  font-weight: bold;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 0.9em;
  font-weight: 500;
  color: #E0E0E0;
  min-width: 60px;
}

.button-group {
  display: flex;
  gap: 4px;
}

.nav-button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85em;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.nav-button.active {
  background: rgba(76, 175, 80, 0.8);
  border-color: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.nav-button.physics-mode {
  background: rgba(156, 39, 176, 0.8);
  border-color: #9C27B0;
  text-transform: capitalize;
}

.nav-button.physics-mode:hover {
  background: rgba(156, 39, 176, 1);
}

.App {
  display: flex;
  position: relative;
  height: calc(100vh - 80px); /* Account for navbar height */
}

.App > * {
  position: relative;
}

.webcam-ai__container {
  width: 50%;
  position: relative;
}

.game {
  width: 50%;
}

.webcam-ai__container > * {
  position: absolute;
  width: 100%;
  z-index: 1;
}

.webcam-ai__canvas {
  z-index: 2;
}

.instructions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px 30px;
  border-radius: 12px;
  z-index: 1000;
  width: 90%;
  max-width: 800px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  max-height: 70vh;
  overflow-y: auto;
}

.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.instructions-header h2 {
  margin: 0;
  font-size: 1.3em;
  color: #4CAF50;
}

.minimize-button {
  background: rgba(255, 107, 107, 0.8);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.3s ease;
}

.minimize-button:hover {
  background: rgba(255, 107, 107, 1);
  transform: scale(1.1);
}

.control-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4CAF50;
}

.control-section h3 {
  margin: 0 0 10px 0;
  font-size: 1.1em;
  color: #FFD700;
}

.control-section ul {
  margin: 0;
  padding-left: 20px;
}

.control-section li {
  margin: 6px 0;
  font-size: 0.9em;
  line-height: 1.4;
}

.instructions h2 {
  margin: 0 0 10px 0;
  font-size: 1.2em;
  color: #4CAF50;
}

.instructions ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.instructions li {
  margin: 8px 0;
  font-size: 0.95em;
}

.instructions li:before {
  content: "•";
  color: #4CAF50;
  font-weight: bold;
  margin-right: 8px;
}

.movement-mode-toggle {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  justify-content: center;
}

.mode-button {
  padding: 8px 16px;
  border: 2px solid #4CAF50;
  background: transparent;
  color: #4CAF50;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  transition: all 0.3s ease;
}

.mode-button:hover {
  background: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

.mode-button.active {
  background: #4CAF50;
  color: white;
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.depth-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 8px;
  font-size: 0.85em;
}

.depth-info p {
  margin: 0;
  color: #4CAF50;
  font-weight: bold;
}

.circular-toggle {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.circular-button {
  padding: 8px 16px;
  border: 2px solid #FF6B6B;
  background: transparent;
  color: #FF6B6B;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: bold;
  transition: all 0.3s ease;
}

.circular-button:hover {
  background: rgba(255, 107, 107, 0.1);
  transform: translateY(-2px);
}

.circular-button.active {
  background: #FF6B6B;
  color: white;
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3); }
  50% { box-shadow: 0 6px 12px rgba(255, 107, 107, 0.5); }
  100% { box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3); }
}

.circular-info {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid #FF6B6B;
  border-radius: 8px;
  font-size: 0.85em;
}

.circular-info p {
  margin: 0;
  color: #FF6B6B;
  font-weight: bold;
}

/* Status Display Styles */
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 0.85em;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  font-size: 10px;
  animation: pulse-status 2s infinite;
}

.status-indicator.good {
  color: #00FF00;
}

.status-indicator.ok {
  color: #FFFF00;
}

.status-indicator.poor {
  color: #FF6B6B;
}

.status-text {
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.8em;
}

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 5px 15px;
  font-size: 0.85em;
  align-items: center;
}

.shortcuts-grid span:nth-child(odd) {
  background: rgba(0, 188, 212, 0.2);
  color: #00BCD4;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  text-align: center;
  min-width: 20px;
}

.shortcuts-grid span:nth-child(even) {
  color: #FFFFFF;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .navbar-controls {
    gap: 10px;
  }

  .control-group {
    flex-direction: column;
    gap: 5px;
  }

  .control-group label {
    min-width: auto;
    font-size: 0.8em;
  }

  .nav-button {
    padding: 5px 8px;
    font-size: 0.75em;
  }

  .instructions {
    width: 95%;
    padding: 15px 20px;
    max-width: none;
  }

  .App {
    height: calc(100vh - 120px); /* Account for taller navbar on mobile */
  }

  .shortcuts-grid {
    gap: 3px 10px;
    font-size: 0.8em;
  }
}
