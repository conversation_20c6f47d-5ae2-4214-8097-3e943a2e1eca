# 3D Game with TensorFlow Hand Detection

An immersive, interactive 3D game built with React, Babylon.js, and TensorFlow.js that lets you control a 3D coral-colored box using intuitive hand gestures through your webcam. This project demonstrates the power of combining modern web technologies with machine learning for creating engaging user experiences.

## 🎮 Project Overview

This application creates a real-time 3D environment where users can:
- Control a 3D object's position using hand movements
- Resize objects through pinch gestures
- Experience smooth, responsive interactions with boundary constraints
- Enjoy a beautiful HDR skybox environment for immersive gameplay

The project showcases the integration of:
- **Computer Vision**: Real-time hand tracking and gesture recognition
- **3D Graphics**: Hardware-accelerated 3D rendering with Babylon.js
- **Machine Learning**: TensorFlow.js Handpose model for accurate hand detection
- **Modern Web Development**: React with Vite for optimal development experience

## ✨ Features

- **Real-time Hand Detection**: Advanced hand tracking using TensorFlow.js Handpose model
- **Gesture-based Controls**: Intuitive pinch and movement gestures for interaction
- **3D Environment**: Beautiful HDR skybox with realistic lighting and materials
- **Enhanced 3D Movement**: Full 3D movement control with depth detection (NEW!)
- **Circular Motion Tracking**: Detects circular finger movements and makes objects follow circular paths (NEW!)
- **Multiple Movement Modes**: Switch between 2D (X,Y), 3D (X,Y,Z), and Circular movement systems
- **Hand Depth Detection**: Uses hand size analysis to control forward/backward movement
- **Direction Detection**: Recognizes clockwise and counterclockwise circular motions
- **Smooth Movement**: Advanced interpolation for fluid object movement in all three dimensions
- **Boundary Constraints**: Intelligent collision detection to keep objects within 3D scene bounds
- **Configurable Sensitivity**: Adjustable sensitivity settings for each axis independently
- **Responsive Design**: Adaptive canvas sizing for different screen resolutions
- **Performance Optimized**: 50ms detection intervals for smooth real-time interaction
- **Visual Feedback**: Real-time hand skeleton visualization with depth indicators

## 🎯 Controls

The game features two movement modes with advanced hand detection through your webcam:

### 2D Movement Mode (Default)
- 👆 **Move Hand**: Control box position in X and Y axes
- 🤏 **Pinch Gesture**: Enter resize mode (thumb tip to index finger tip distance < 30px)
- ✋ **While Pinched**: Move hand apart/together to dynamically resize the box
- 👋 **Release Pinch**: Return to movement mode with smooth transitions

### 3D Movement Mode (Enhanced)
- 👆 **Move Hand**: Control box position in X, Y, and Z axes
- 🔄 **Move Closer/Farther**: Control depth (Z-axis) by moving hand toward/away from camera
- 📏 **Hand Size Detection**: Larger hand = closer (positive Z), smaller hand = farther (negative Z)
- 🤏 **Pinch Gesture**: Enter resize mode (works in both 2D and 3D modes)
- ✋ **While Pinched**: Move hand apart/together to dynamically resize the box
- 👋 **Release Pinch**: Return to movement mode with smooth 3D transitions
- 🎯 **Hand Tracking**: Uses index finger base (landmark 5) as primary position reference
- 📊 **Visual Depth Indicator**: Real-time depth bar showing hand distance from camera

### Circular Motion Mode (NEW!)
- 🔄 **Draw Circles**: Move your index finger in circular motions to make the object follow circular paths
- ↻ **Direction Detection**: Automatically detects clockwise and counterclockwise movements
- 📐 **Radius Control**: Larger finger circles create wider object movement patterns
- 🎯 **Precision Tracking**: Uses index finger tip (landmark 8) for precise circular path detection
- 🌊 **Smooth Animation**: Object follows detected circular paths with smooth interpolation
- 📊 **Visual Feedback**: Real-time circular path visualization with direction indicators
- ⚡ **Adaptive Detection**: Configurable sensitivity for different circle sizes and speeds
- 🔄 **Height Variation**: Z-axis movement creates 3D spiral effects during circular motion

### Mode Switching
- 🔄 **Cycle Modes**: Click mode buttons to cycle through "2D Mode" → "3D Mode" → "🔄 Circular"
- ⭕ **Circular Toggle**: Independent circular motion toggle that works with any base mode
- ⚡ **Real-time Switching**: Change modes during gameplay without interruption
- 🎮 **Hybrid Modes**: Combine circular motion with 2D or 3D movement for complex interactions

## 📋 Prerequisites

### System Requirements
- **Node.js**: Version 14.0 or higher (recommended: 16.x or 18.x)
- **Yarn**: Package manager (v1.22.x or higher)
- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)

### Browser Compatibility
- **Chrome**: Version 88+ (recommended for best performance)
- **Firefox**: Version 85+
- **Safari**: Version 14+ (macOS only)
- **Edge**: Version 88+

### Hardware Requirements
- **Webcam**: Any USB or built-in camera (720p or higher recommended)
- **GPU**: WebGL 2.0 compatible graphics card
- **RAM**: Minimum 4GB (8GB recommended for optimal performance)
- **CPU**: Modern multi-core processor (Intel i5/AMD Ryzen 5 or equivalent)

### Browser Permissions
- **Camera Access**: Required for hand detection
- **WebGL**: Must be enabled for 3D rendering
- **JavaScript**: Must be enabled

## 🚀 Installation Instructions

### Quick Start

1. **Clone the repository**:
```bash
git clone https://github.com/yourusername/3d_Game_Tensorflow.git
cd 3d_Game_Tensorflow
```

2. **Install dependencies** (using Yarn - required):
```bash
yarn install
```

3. **Start the development server**:
```bash
yarn dev
```

4. **Open your browser** and navigate to:
```
http://localhost:3000
```

5. **Grant camera permissions** when prompted by your browser

### Detailed Setup Guide

#### Step 1: Environment Preparation
```bash
# Verify Node.js version
node --version  # Should be 14.0 or higher

# Verify Yarn installation
yarn --version  # Should be 1.22.x or higher

# If Yarn is not installed:
npm install -g yarn
```

#### Step 2: Project Setup
```bash
# Clone with specific branch (if needed)
git clone -b main https://github.com/yourusername/3d_Game_Tensorflow.git

# Navigate to project directory
cd 3d_Game_Tensorflow

# Verify project structure
ls -la  # Should see package.json, src/, etc.
```

#### Step 3: Dependency Installation
```bash
# Clean install (recommended)
yarn install --frozen-lockfile

# Alternative: Force clean install
rm -rf node_modules yarn.lock
yarn install
```

#### Step 4: Development Server
```bash
# Start with verbose output
yarn dev --host

# Or start with specific port
yarn dev --port 3000
```

### Package Manager Requirements

> **⚠️ Important**: This project **requires Yarn** as the package manager. NPM is not supported due to:
> - Specific dependency resolution requirements
> - Yarn workspace configurations
> - Lock file compatibility with Babylon.js modules

### Verification Steps

After installation, verify everything works:

1. **Check browser console** for any errors
2. **Test camera access** - you should see your webcam feed
3. **Verify hand detection** - move your hand to see green skeleton overlay
4. **Test 3D rendering** - you should see a coral-colored box in 3D space

## 📖 Usage Guide

### Getting Started

1. **Launch the application** using `yarn dev`
2. **Allow camera access** when prompted by your browser
3. **Position yourself** approximately arm's length from the camera
4. **Ensure good lighting** for optimal hand detection
5. **Hold your hand** in front of the camera with palm facing forward

### Interaction Guide

#### Basic Movement
- **Position your hand** in the camera view
- **Move your hand** left/right/up/down to control the box position
- The box will **smoothly follow** your hand movements with a slight delay for stability
- **Boundary constraints** prevent the box from leaving the visible scene area

#### Resize Mode
1. **Make a pinch gesture** by bringing your thumb and index finger together
2. **Hold the pinch** - you'll enter resize mode
3. **While pinching**, move your hand to spread fingers apart (larger box) or together (smaller box)
4. **Release the pinch** to return to movement mode

#### Advanced Tips
- **Smooth movements** work better than rapid gestures
- **Keep your hand** within the camera frame for consistent tracking
- **Use good lighting** to improve detection accuracy
- **Maintain consistent distance** from camera for best results

### Expected Behavior

- **Hand Detection**: Green skeleton overlay appears on your hand
- **Box Movement**: Coral-colored box follows hand position in 3D space
- **Resize Feedback**: Box scales smoothly during pinch gestures
- **Performance**: 50ms detection intervals for real-time responsiveness

## 🛠️ Technology Stack

### Core Technologies
- **Frontend Framework**: React 17.0.0 - Component-based UI architecture
- **3D Engine**: Babylon.js 4.2.0 - WebGL-based 3D rendering engine
- **Machine Learning**: TensorFlow.js 3.7.0 - Browser-based ML inference
- **Hand Detection**: Handpose Model 0.0.7 - 21-point hand landmark detection
- **Build Tool**: Vite 2.3.8 - Fast development and build tooling
- **Package Manager**: Yarn 1.22.22 - Dependency management

### Supporting Libraries
- **Camera Integration**: React Webcam 5.2.4 - Webcam access and streaming
- **3D GUI**: Babylon.js GUI 4.2.0 - 3D user interface components
- **Asset Loading**: Babylon.js Loaders 4.2.0 - 3D model and texture loading
- **Environment Maps**: HDR Cube Textures - Realistic lighting and reflections

### Development Tools
- **Hot Module Replacement**: Vite React Refresh Plugin
- **Asset Processing**: HDR file support for environment mapping
- **Build Optimization**: Asset inlining and bundling configuration

## 📁 Project Structure

```
3d_Game_Tensorflow/
├── 📁 src/                          # Source code directory
│   ├── 📁 utils/                    # Utility modules and game logic
│   │   ├── 🎮 game.js              # Main Babylon.js game engine class
│   │   ├── 🤖 index.js             # Hand detection and gesture processing
│   │   ├── 🚀 freeMovement.js      # Enhanced 3D movement system (NEW!)
│   │   ├── 🔄 circularMotion.js    # Circular motion detection and tracking (NEW!)
│   │   ├── 📚 movement3DExample.js # 3D movement examples and tests (NEW!)
│   │   ├── 🎯 circularMotionExample.js # Circular motion examples and tests (NEW!)
│   │   ├── 🌅 moonlit_golf_2k.hdr  # HDR environment map for skybox
│   │   └── 🌃 satara_night_1k.hdr  # Alternative HDR environment map
│   ├── ⚛️ App.jsx                   # Main React application component
│   ├── 🎨 App.css                   # Application styling
│   ├── 🚀 main.jsx                  # React application entry point
│   ├── 🎨 index.css                 # Global styles
│   ├── 🖼️ favicon.svg               # Application favicon
│   └── 🖼️ logo.svg                  # Application logo
├── 📁 models/                       # 3D model assets
│   └── 🚢 ship.obj                  # 3D ship model (currently unused)
├── 📁 node_modules/                 # Installed dependencies (auto-generated)
├── 📄 index.html                    # HTML entry point
├── 📦 package.json                  # Project configuration and dependencies
├── 🔒 package-lock.json             # NPM lock file (auto-generated)
├── 🧶 yarn.lock                     # Yarn lock file (dependency versions)
├── ⚙️ vite.config.js                # Vite build tool configuration
└── 📖 README.md                     # Project documentation (this file)
```

### Key Files Description

#### Core Application Files
- **`src/App.jsx`**: Main React component that orchestrates webcam, hand detection, and 3D game with mode switching
- **`src/main.jsx`**: Application entry point that renders the React app
- **`src/utils/game.js`**: Babylon.js game engine class handling 3D scene, camera, lighting, and dual movement modes
- **`src/utils/index.js`**: Hand detection utilities with gesture recognition, landmark processing, and depth detection
- **`src/utils/freeMovement.js`**: Enhanced 3D movement system with depth detection and configurable sensitivity
- **`src/utils/circularMotion.js`**: Circular motion detection system with direction recognition and path tracking
- **`src/utils/movement3DExample.js`**: Examples and test cases for the 3D movement system
- **`src/utils/circularMotionExample.js`**: Examples and test cases for the circular motion system

#### Configuration Files
- **`vite.config.js`**: Build configuration with HDR asset support and React refresh
- **`package.json`**: Project metadata, dependencies, and npm scripts
- **`index.html`**: HTML template with root div and module script loading

#### Asset Files
- **`src/utils/*.hdr`**: High Dynamic Range environment maps for realistic 3D lighting
- **`models/ship.obj`**: 3D model file (prepared for future features)
- **`src/*.css`**: Styling for webcam layout, game canvas, and UI components

## ⚙️ Configuration

### Vite Configuration (`vite.config.js`)

The project uses a custom Vite configuration optimized for 3D assets and React development:

```javascript
export default defineConfig({
  plugins: [reactRefresh()],           // Hot module replacement for React
  assetsInclude: ['**/*.hdr'],         // Include HDR files as assets
  build: {
    assetsInlineLimit: 0,              // Prevent asset inlining for large files
  }
})
```

### Environment Variables

Currently, the project doesn't require environment variables, but you can create a `.env` file for customization:

```bash
# .env (optional)
VITE_DETECTION_INTERVAL=50           # Hand detection interval in ms
VITE_PINCH_THRESHOLD=30              # Pinch detection threshold in pixels
VITE_SCENE_WIDTH=100                 # 3D scene width for coordinate mapping
VITE_SCENE_HEIGHT=80                 # 3D scene height for coordinate mapping
```

### Customization Options

#### Hand Detection Parameters
In `src/utils/index.js`, you can adjust:
- **Pinch threshold**: `pinchDistance < 30` (line 68)
- **Hand landmark points**: Modify `fingerJoints` object for different tracking
- **Detection sensitivity**: Adjust distance calculations

#### 3D Scene Configuration
In `src/utils/game.js`, you can modify:
- **Box size limits**: `boxMinSize` and `boxMaxSize` (lines 32-33)
- **Movement speed**: `moveSpeed` and `lerpFactor` (lines 34, 183)
- **Scene boundaries**: `sceneWidth` and `sceneHeight` (lines 160-161)
- **Camera position**: `UniversalCamera` constructor (line 42)

#### Visual Customization
- **Box color**: Change `#FF7F50` in `generateMeshes()` method
- **Lighting**: Modify `HemisphericLight` properties in `addLighting()`
- **Skybox**: Replace HDR files in `src/utils/` directory

## 💡 Examples

### Basic Integration Example

```javascript
// Example: Adding a new 3D object to the scene
import { Mesh, StandardMaterial, Color3 } from "babylonjs";

// In your Game class
addSphere() {
  const sphere = Mesh.CreateSphere("sphere", 16, 3, this.scene);
  const material = new StandardMaterial("sphereMat", this.scene);
  material.diffuseColor = new Color3(0, 1, 0); // Green color
  sphere.material = material;
  sphere.position.x = 10;
  return sphere;
}
```

### Custom Gesture Recognition

```javascript
// Example: Adding a new gesture in src/utils/index.js
const detectWave = (landmarks) => {
  const wrist = landmarks[0];
  const indexTip = landmarks[8];
  const middleTip = landmarks[12];

  // Simple wave detection based on finger positions
  const isWaving = Math.abs(indexTip[0] - middleTip[0]) > 50;
  return isWaving;
};
```

### Enhanced 3D Movement System

```javascript
// Example: Using the new 3D movement system
import { createMovement3D } from './utils/freeMovement.js';

// Create 3D movement controller with custom configuration
const movement3D = createMovement3D({
  sensitivity: { x: 0.8, y: 0.8, z: 0.6 },
  interpolation: { x: 0.12, y: 0.12, z: 0.08 },
  boundaries: {
    x: { min: -50, max: 50 },
    y: { min: -40, max: 40 },
    z: { min: -30, max: 30 }
  }
});

// Update object position with enhanced hand state
const handState3D = {
  position2D: { x: 320, y: 240 },
  handSize: 120,
  depth: 15,
  confidence: 0.95
};

movement3D.updateObjectPosition(handState3D, babylonObject);
```

### Circular Motion System

```javascript
// Example: Using the circular motion detection system
import { createCircularMotion } from './utils/circularMotion.js';

// Create circular motion controller
const circularMotion = createCircularMotion({
  detection: {
    minPoints: 8,           // Minimum points to detect circle
    radiusThreshold: 30,    // Minimum radius in pixels
    angleThreshold: Math.PI * 1.5, // Minimum angle coverage
    maxRadiusVariation: 0.3 // Maximum radius variation allowed
  },
  motion: {
    followSpeed: 0.2,       // How quickly object follows
    radiusScale: 0.5,       // Scale factor for 3D radius
    heightVariation: 10     // Z-axis variation during motion
  }
});

// Update with finger position (index finger tip)
circularMotion.updatePosition({ x: fingerX, y: fingerY });

// Apply circular motion to 3D object
circularMotion.applyCircularMotion(babylonObject);

// Get current state
const state = circularMotion.getState();
console.log('Circular motion active:', state.isActive);
console.log('Direction:', state.motionState.direction > 0 ? 'CCW' : 'CW');
```

### Custom 3D Movement Configuration

```javascript
// Example: Gaming-optimized configuration
const gamingConfig = {
  sensitivity: { x: 1.5, y: 1.3, z: 1.0 },    // High sensitivity
  interpolation: { x: 0.25, y: 0.25, z: 0.15 }, // Responsive movement
  boundaries: {
    x: { min: -70, max: 70 },
    y: { min: -55, max: 55 },
    z: { min: -35, max: 35 }
  }
};

// Example: Precision-optimized configuration
const precisionConfig = {
  sensitivity: { x: 0.5, y: 0.5, z: 0.3 },     // Low sensitivity
  interpolation: { x: 0.05, y: 0.05, z: 0.03 }, // Very smooth
  depth: { smoothingFactor: 0.08 }               // Ultra-smooth depth
};
```

### Performance Monitoring

```javascript
// Example: Adding FPS counter to your game
// In src/utils/game.js constructor
this.engine.runRenderLoop(() => {
  this.scene.render();

  // Log FPS every second
  if (Date.now() - this.lastFPSLog > 1000) {
    console.log(`FPS: ${this.engine.getFps().toFixed(2)}`);
    this.lastFPSLog = Date.now();
  }
});
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 🎥 Camera and Hand Detection Issues

**Problem**: No hand detection or webcam not working
```bash
# Solutions:
1. Check camera permissions in browser settings
2. Verify camera is not being used by another application
3. Try a different browser (Chrome recommended)
4. Check browser console for camera access errors
```

**Problem**: Poor hand detection accuracy
```bash
# Solutions:
1. Improve lighting conditions (avoid backlighting)
2. Position yourself 2-3 feet from camera
3. Ensure hand is fully visible in camera frame
4. Clean camera lens
5. Use a solid background behind your hand
```

**Problem**: Hand detection is laggy or slow
```bash
# Solutions:
1. Close other resource-intensive applications
2. Reduce detection interval in code (increase from 50ms)
3. Check CPU usage in browser task manager
4. Try lowering webcam resolution
```

#### 🎮 3D Rendering Issues

**Problem**: Black screen or no 3D content
```bash
# Check WebGL support:
1. Visit: chrome://gpu/ (Chrome) or about:support (Firefox)
2. Look for WebGL status
3. Update graphics drivers
4. Enable hardware acceleration in browser settings
```

**Problem**: Poor 3D performance or low FPS
```bash
# Solutions:
1. Update graphics drivers
2. Close other GPU-intensive applications
3. Reduce browser zoom level
4. Try different browser
5. Check GPU temperature and throttling
```

**Problem**: 3D objects not responding to hand movements
```bash
# Debug steps:
1. Check browser console for JavaScript errors
2. Verify hand detection is working (green skeleton visible)
3. Check if box is visible in 3D scene
4. Verify camera permissions are granted
```

#### 📦 Installation and Build Issues

**Problem**: `yarn install` fails
```bash
# Solutions:
1. Clear yarn cache: yarn cache clean
2. Delete node_modules: rm -rf node_modules
3. Delete yarn.lock: rm yarn.lock
4. Reinstall: yarn install
5. Check Node.js version: node --version (should be 14+)
```

**Problem**: Build errors with Babylon.js or TensorFlow
```bash
# Solutions:
1. Ensure you're using Yarn (not NPM)
2. Clear browser cache and hard refresh
3. Check for conflicting global packages
4. Try: yarn install --frozen-lockfile
```

**Problem**: Vite dev server won't start
```bash
# Solutions:
1. Check if port 5173 is already in use
2. Try different port: yarn dev --port 3000
3. Check firewall settings
4. Verify Vite installation: yarn list vite
```

#### 🌐 Browser Compatibility Issues

**Problem**: Features not working in specific browsers
```bash
# Browser-specific solutions:

Safari:
- Enable "Develop" menu and check "Disable WebGL"
- Update to latest Safari version
- Check camera permissions in System Preferences

Firefox:
- Type about:config and search for "webgl"
- Ensure webgl.disabled is set to false
- Check privacy settings for camera access

Edge:
- Reset browser settings
- Clear browsing data
- Check Windows camera privacy settings
```

### 🔍 Debug Mode

Enable debug mode for detailed logging:

```javascript
// Add to src/utils/game.js constructor
this.debug = true;

// Add to moveBox method
if (this.debug) {
  console.log('Hand State:', handState);
  console.log('Box Position:', this.box1.position);
}
```

### 📊 Performance Monitoring

Monitor performance metrics:

```javascript
// Add to browser console
setInterval(() => {
  console.log({
    fps: engine.getFps(),
    memory: performance.memory?.usedJSHeapSize,
    drawCalls: engine.getGlInfo().drawCalls
  });
}, 5000);
```

## 📜 Available Scripts

### Development Commands

```bash
# Start development server with hot reload
yarn dev
# Starts Vite dev server on http://localhost:5173
# Includes hot module replacement (HMR) for instant updates

# Start dev server on custom port
yarn dev --port 3000

# Start dev server with network access
yarn dev --host
# Allows access from other devices on your network
```

### Build Commands

```bash
# Build for production
yarn build
# Creates optimized production build in dist/ directory
# Includes asset optimization and code minification

# Preview production build locally
yarn preview
# Serves the production build for testing
# Useful for testing before deployment
```

### Utility Commands

```bash
# Clean install dependencies
yarn install --frozen-lockfile

# Update dependencies (use with caution)
yarn upgrade

# Check for outdated packages
yarn outdated

# Clear yarn cache
yarn cache clean

# Analyze bundle size
yarn build && npx vite-bundle-analyzer dist
```

### Development Workflow

```bash
# Typical development session
git pull origin main          # Get latest changes
yarn install                  # Install any new dependencies
yarn dev                      # Start development server
# Make your changes...
yarn build                    # Test production build
yarn preview                  # Preview production build
```

## 🤝 Contributing Guidelines

We welcome contributions to make this 3D hand-controlled game even better! Here's how you can contribute:

### Development Setup

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
```bash
git clone https://github.com/yourusername/3d_Game_Tensorflow.git
cd 3d_Game_Tensorflow
```

3. **Set up the development environment**:
```bash
yarn install
yarn dev
```

4. **Create a feature branch**:
```bash
git checkout -b feature/amazing-new-feature
```

### Contribution Areas

We're particularly interested in contributions for:

- **🎮 New Gestures**: Additional hand gestures and interactions
- **🎨 Visual Enhancements**: Better materials, lighting, or effects
- **🚀 Performance**: Optimization improvements
- **🔧 Bug Fixes**: Issue resolution and stability improvements
- **📱 Mobile Support**: Touch controls and responsive design
- **🎯 Accessibility**: Making the game more accessible
- **📚 Documentation**: Improving guides and examples

### Code Standards

#### JavaScript/React Guidelines
```javascript
// Use modern ES6+ syntax
const handleGesture = (gesture) => {
  // Prefer const/let over var
  const { position, isPinched } = gesture;

  // Use descriptive variable names
  const normalizedPosition = mapCoordinates(position);

  // Add comments for complex logic
  // Map webcam coordinates to 3D scene space
  return normalizedPosition;
};
```

#### File Organization
- **Components**: Keep React components in `src/`
- **Utilities**: Game logic and helpers in `src/utils/`
- **Assets**: 3D models and textures in appropriate directories
- **Styles**: CSS files alongside components

#### Commit Message Format
```bash
# Use conventional commit format
git commit -m "feat: add new pinch-to-rotate gesture"
git commit -m "fix: resolve hand detection lag on Firefox"
git commit -m "docs: update installation instructions"
git commit -m "perf: optimize 3D rendering loop"
```

### Testing Guidelines

Before submitting a PR:

1. **Test across browsers**: Chrome, Firefox, Safari, Edge
2. **Verify hand detection**: Test with different lighting conditions
3. **Check performance**: Monitor FPS and memory usage
4. **Test edge cases**: Poor lighting, multiple hands, etc.

```bash
# Run these checks before submitting
yarn build          # Ensure production build works
yarn preview         # Test production build locally
```

### Pull Request Process

1. **Update documentation** if you've changed functionality
2. **Add examples** for new features
3. **Test thoroughly** across different environments
4. **Write clear PR description** explaining changes
5. **Link related issues** if applicable

#### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Performance improvement
- [ ] Documentation update

## Testing
- [ ] Tested in Chrome
- [ ] Tested in Firefox
- [ ] Tested with different lighting
- [ ] Performance impact assessed

## Screenshots/Videos
(If applicable)
```

### Development Tips

- **Use browser dev tools** for debugging 3D performance
- **Test with different cameras** and lighting conditions
- **Monitor console** for TensorFlow.js warnings
- **Profile performance** using browser tools

### Getting Help

- **Open an issue** for bugs or feature requests
- **Join discussions** in existing issues
- **Ask questions** in pull request comments
- **Check existing code** for patterns and conventions

## 📄 License

### MIT License

```
MIT License

Copyright (c) 2025 GreenHacker

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### Third-Party Licenses

This project uses several open-source libraries:

- **React**: MIT License
- **Babylon.js**: Apache License 2.0
- **TensorFlow.js**: Apache License 2.0
- **Vite**: MIT License

### Usage Rights

✅ **You can**:
- Use this project for personal and commercial purposes
- Modify and distribute the code
- Create derivative works
- Use it for educational purposes

❌ **You cannot**:
- Hold the authors liable for any damages
- Use the authors' names for endorsement without permission

---

## 🙏 Acknowledgments

- **TensorFlow.js Team** for the amazing Handpose model
- **Babylon.js Community** for the powerful 3D engine
- **React Team** for the excellent frontend framework
- **Vite Team** for the lightning-fast build tool
- **Open Source Community** for inspiration and support

---

<div align="center">

**Made with ❤️ by [GreenHacker](https://github.com/GreenHacker420)**

*If you found this project helpful, please consider giving it a ⭐ on GitHub!*

[🐛 Report Bug](https://github.com/yourusername/3d_Game_Tensorflow/issues) • [✨ Request Feature](https://github.com/yourusername/3d_Game_Tensorflow/issues) • [💬 Discussions](https://github.com/yourusername/3d_Game_Tensorflow/discussions)

</div>